package timehelpers

import (
	"strings"
	"time"
)

const (
	RFC1123NoSecNoTZ = "Mon, 02 Jan 2006 15:04" // RFC1123 with numeric zone

	RFC1123Notif     = "Mon, 02 Jan 2006 15:04:05 UTC-07" // RFC1123 with numeric zone
	DDMMYYHHMMWithTZ = "02/01/06 15:04 UTC-07"            // RFC1123 with numeric zone
)

type TimeTasmotaWIB time.Time

func (c *TimeTasmotaWIB) UnmarshalJSON(b []byte) error {
	value := strings.Trim(string(b), `"`) //get rid of "
	if value == "" || value == "null" {
		return nil
	}

	loc, _ := time.LoadLocation("Asia/Jakarta")
	t, err := time.ParseInLocation("2006-01-02T15:04:05", value, loc) //parse time
	if err != nil {
		return err
	}
	*c = TimeTasmotaWIB(t) //set result using the pointer
	return nil
}

func (c TimeTasmotaWIB) MarshalJSON() ([]byte, error) {
	return []byte(`"` + time.Time(c).Format(time.RFC3339Nano) + `"`), nil
}

func TruncateDayLocal(t time.Time) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, time.Local)
}
