package timehelpers

import (
	"assetfindr/pkg/common/commonlogger"
	"time"
)

func ParseDateFilter(dateString string, isEndDate bool) (time.Time, error) {
	layout := "2006-01-02"

	parsedDate, err := time.ParseInLocation(layout, dateString, time.Local)
	if err != nil {
		return time.Time{}, err
	}

	if isEndDate {
		parsedDate = time.Date(
			parsedDate.Year(),
			parsedDate.Month(),
			parsedDate.Day(),
			23, 59, 59, 0, // hour, minute, second, nanosecond
			parsedDate.Location(),
		)
	}

	return parsedDate, nil
}

func ParseDateTime(dateString string) (time.Time, error) {
	layout := "2006-01-02 15:04:05"
	location, err := time.LoadLocation("Asia/Bangkok") // UTC+7
	if err != nil {
		commonlogger.Warnf("error parse date, load location %v", err)
		return time.Time{}, err
	}

	parsedDate, err := time.ParseInLocation(layout, dateString, location)
	if err != nil {
		commonlogger.Warnf("error parse date %v", err)
		return time.Time{}, err
	}
	return parsedDate, nil
}

func GetShiftCode(t time.Time) string {
	outboundTime := time.Date(0, 1, 1, t.Hour(), t.Minute(), t.Second(), 0, time.UTC)

	dayStart := time.Date(0, 1, 1, 7, 0, 0, 0, time.UTC)
	dayEnd := time.Date(0, 1, 1, 19, 0, 0, 0, time.UTC)

	if !outboundTime.Before(dayStart) && outboundTime.Before(dayEnd) {
		return "DS"
	}
	return "NS"
}
