package tmplhelpers

import (
	"fmt"
	"html/template"
)

type TicketNoteBody struct {
	Adder, Subject, AssetName, Note, Status string
	RedirectWOLink                          template.URL
	BodyEmail, BodyPushNotif                string
	TitleEmail, TitlePushNotif              string
	PlatNo, CustomerName                    string
	IsFromWorkshop                          bool
}

func (u *TicketNoteBody) ConstructSubjectPushNotif() string {
	if u.IsFromWorkshop {
		return u.constructSubjectPushNotifWorkshop()
	}

	u.TitlePushNotif = "New Work Order Note Added"

	return u.TitlePushNotif

}
func (u *TicketNoteBody) ConstructBodyPushNotif() string {
	if u.IsFromWorkshop {
		return u.constructBodyPushNotifWorkshop()
	}

	bodyPushNotif := fmt.Sprintf("%s has just added a new note to the work order %s on %s", u.Adder, u.Subject, u.AssetName)

	return bodyPushNotif

}

func (u *TicketNoteBody) ConstructSubjectEmail() string {
	if u.IsFromWorkshop {
		return u.constructSubjectEmailWorkshop()
	}

	u.TitleEmail = fmt.Sprintf("New Note Added to Work Order %s for %s", u.Subject, u.AssetName)

	return u.TitleEmail
}

func (u *TicketNoteBody) ConstructBodyEmail() string {
	if u.IsFromWorkshop {
		return u.constructBodyEmailWorkshop()
	}

	// Typing after dear ...
	bodyEmail := "We wanted to let you know that changes have been made to your work order. Here are the details of the updates:"

	// add spacing
	bodyEmail += "<br><br>"

	// add table
	templateTable := `<table border="0" cellpadding="10" cellspacing="0"><tr><td>Asset</td><td>{{.AssetName}}</td></tr><tr><td>Work Order Subject</td><td>{{.Subject}}</td></tr><tr><td>Status</td><td>{{.Status}}</td></tr><tr><td>Note Added by</td><td>{{.Adder}}</td></tr><tr><td>New Note</td><td>{{.Note}}</td></tr></table><br>For a complete overview of the work order and the changes, please click the button below:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return bodyEmail + tableBody
}

/*
	WorkShop Area
*/

func (u *TicketNoteBody) constructSubjectPushNotifWorkshop() string {
	u.TitlePushNotif = "Workshop - New Work Order Note Added"

	return u.TitlePushNotif

}
func (u *TicketNoteBody) constructBodyPushNotifWorkshop() string {
	bodyPushNotif := fmt.Sprintf("%s has just added a new note to the work order for %s - %s.", u.Adder, u.PlatNo, u.CustomerName)

	return bodyPushNotif

}

func (u *TicketNoteBody) constructSubjectEmailWorkshop() string {
	u.TitleEmail = fmt.Sprintf("Workshop - New Note Added to Work Order %s for %s - %s", u.Subject, u.PlatNo, u.CustomerName)

	return u.TitleEmail
}

func (u *TicketNoteBody) constructBodyEmailWorkshop() string {
	// Typing after dear ...
	bodyEmail := "We wanted to inform you that a new note has been added to your work order. Here are the details:"

	// add spacing
	bodyEmail += "<br><br>"

	// add table
	templateTable := `<table border="0" cellpadding="10" cellspacing="0"><tr><td>Vehicle</td><td>{{.PlatNo}} - {{.CustomerName}}</td></tr><td>Status</td><td>{{.Status}}</td><tr><td>Note Added by</td><td>{{.Adder}}</td></tr><tr><td>New Note</td><td>{{.Note}}</td></tr></table><br>For a complete overview of the work order and the changes, please click the button below:<br><a href="{{.RedirectWOLink}}"><button style="background-color:#165fff;color:#fff;border:none;border-radius:5px;padding:10px 20px;cursor:pointer">View Work Order Ticket</button></a>`

	tableBody, _ := ParseStringTemplate(templateTable, u)

	return bodyEmail + tableBody
}
