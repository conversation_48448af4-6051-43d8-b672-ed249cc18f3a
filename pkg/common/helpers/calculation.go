package helpers

import "math"

func CalculateAverageRTD(RDT1 float64, RDT2 float64, RDT3 float64, RDT4 float64) float64 {
	totalRTD := RDT1
	divider := 1

	if RDT2 > 0 {
		totalRTD += RDT2
		divider += 1
	}

	if RDT3 > 0 {
		totalRTD += RDT3
		divider += 1
	}

	if RDT4 > 0 {
		totalRTD += RDT4
		divider += 1
	}

	averageRTD := totalRTD / float64(divider)

	return averageRTD
}

func CalculateTyreUtilRate(otd float64, avgRTD float64) float64 {
	if otd == 0 {
		return 0
	}

	avgRTD = math.Min(avgRTD, otd)
	return ((otd - avgRTD) / otd) * 100
}

func CalculateTyreProjectedLife(totalKM int, std, avgRTD float64) int {
	if std == 0 {
		return 0
	}

	avgRTD = math.Min(avgRTD, std)
	t := std - avgRTD
	if t == 0 {
		return 0
	}

	projectedLifeRemainingKM := int(float64(totalKM) * avgRTD / t)
	return totalKM + projectedLifeRemainingKM
}

func CalculateTyreProjectedLifeHM(totalHM float64, std, avgRTD float64) float64 {
	if std == 0 {
		return 0
	}

	avgRTD = math.Min(avgRTD, std)
	t := std - avgRTD
	if t == 0 {
		return totalHM
	}

	projectedLifeRemainingHM := totalHM * avgRTD / t
	return totalHM + projectedLifeRemainingHM
}

func CalculateTyreTreadRunningCost(otd, startThreadDepth, averageRTD float64, amountCost int) float64 {
	if otd == 0 {
		return 0
	}

	costPerMm := float64(amountCost) / float64(otd)
	mmUsed := startThreadDepth - averageRTD
	return costPerMm * mmUsed
}
