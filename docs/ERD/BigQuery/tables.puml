@startuml 
hide circle

entity "**can_bus_sesor_data_v2**" {
  asset_id: STRING NULLABLE
  --
  time: TIMESTAMP NULLABLE
  ident: STRING NULLABLE
  integration_id: STRING NULLABLE
  created_at: TIMESTAMP NULLABLE
  client_id: STRING NULLABLE
  can_abs_failure_indicator_status: BOOLEAN NULLABLE
  can_additional_front_lights_status: BOOLEAN NULLABLE
  can_additional_rear_lights_status: BOOLEAN NULLABLE
  can_air_condition_status: BOOLEAN NULLABLE
  can_airbag_indicator_status: BOOLEAN NULLABLE
  can_automatic_retarder_status: BOOLEAN NULLABLE
  can_battery_indicator_status: BOOLEAN NULLABLE
  can_car_closed_remote_status: BOOLEAN NULLABLE
  can_car_closed_status: BOOLEAN NULLABLE
  can_central_differential_4_hi_status: BOOLEAN NULLABLE
  can_central_differential_4_lo_status: BOOLEAN NULLABLE
  can_check_engine_indicator_status: BOOLEAN NULLABLE
  can_cng_status: BOOLEAN NULLABLE
  can_connection_state_1: INTEGER NULLABLE
  can_connection_state_2: INTEGER NULLABLE
  can_connection_state_3: INTEGER NULLABLE
  can_coolant_level_low_indicator_status: BOOLEAN NULLABLE
  can_cruise_status: BOOLEAN NULLABLE
  can_drive_gear_status: BOOLEAN NULLABLE
  can_driver_seatbelt_indicator_status: BOOLEAN NULLABLE
  can_driver_seatbelt_status: BOOLEAN NULLABLE
  can_dynamic_ignition_status: BOOLEAN NULLABLE
  can_electric_engine_status: BOOLEAN NULLABLE
  can_electronic_power_control_status: BOOLEAN NULLABLE
  can_engine_ignition_status: BOOLEAN NULLABLE
  can_engine_load_level: INTEGER NULLABLE
  can_engine_lock_status: BOOLEAN NULLABLE
  can_engine_motorhours: FLOAT NULLABLE
  can_engine_rpm: INTEGER NULLABLE
  can_engine_temperature: FLOAT NULLABLE
  can_engine_working_status: BOOLEAN NULLABLE
  can_eps_indicator_status: BOOLEAN NULLABLE
  can_esp_indicator_status: BOOLEAN NULLABLE
  can_esp_status: BOOLEAN NULLABLE
  can_factory_armed_status: BOOLEAN NULLABLE
  can_front_differential_status: BOOLEAN NULLABLE
  can_front_fog_lights_status: BOOLEAN NULLABLE
  can_front_left_door_status: BOOLEAN NULLABLE
  can_front_passenger_seatbelt_status: BOOLEAN NULLABLE
  can_front_passenger_status: BOOLEAN NULLABLE
  can_front_right_door_status: BOOLEAN NULLABLE
  can_fuel_consumed: FLOAT NULLABLE
  can_fuel_level_low_indicator_status: BOOLEAN NULLABLE
  can_glow_plug_indicator_status: BOOLEAN NULLABLE
  can_handbrake_indicator_status: BOOLEAN NULLABLE
  can_handbrake_status: BOOLEAN NULLABLE
  can_high_beam_status: BOOLEAN NULLABLE
  can_hood_status: BOOLEAN NULLABLE
  can_ignition_key_status: BOOLEAN NULLABLE
  can_interlock_active: BOOLEAN NULLABLE
  can_light_signal_status: BOOLEAN NULLABLE
  can_lights_failure_indicator_status: BOOLEAN NULLABLE
  can_lights_hazard_lights_status: BOOLEAN NULLABLE
  can_low_beam_status: BOOLEAN NULLABLE
  can_maintenance_required_status: BOOLEAN NULLABLE
  can_manual_retarder_status: BOOLEAN NULLABLE
  can_module_sleep_mode: BOOLEAN NULLABLE
  can_neutral_gear_status: BOOLEAN NULLABLE
  can_oil_pressure_indicator_status: BOOLEAN NULLABLE
  can_operator_present_status: BOOLEAN NULLABLE
  can_parking_lights_status: BOOLEAN NULLABLE
  can_parking_status: BOOLEAN NULLABLE
  can_passenger_seatbelt_indicator_status: BOOLEAN NULLABLE
  can_pedal_brake_status: BOOLEAN NULLABLE
  can_pedal_clutch_status: BOOLEAN NULLABLE
  can_private_status: BOOLEAN NULLABLE
  can_program_id: INTEGER NULLABLE
  can_pto_status: BOOLEAN NULLABLE
  can_ready_to_drive_indicator_status: BOOLEAN NULLABLE
  can_rear_central_passenger_seatbelt_status: BOOLEAN NULLABLE
  can_rear_differential_status: BOOLEAN NULLABLE
  can_rear_fog_lights_status: BOOLEAN NULLABLE
  can_rear_left_door_status: BOOLEAN NULLABLE
  can_rear_left_passenger_seatbelt_status: BOOLEAN NULLABLE
  can_rear_right_door_status: BOOLEAN NULLABLE
  can_rear_right_passenger_seatbelt_status: BOOLEAN NULLABLE
  can_reverse_gear_status: BOOLEAN NULLABLE
  can_roof_opened_status: BOOLEAN NULLABLE
  can_soot_filter_indicator_status: BOOLEAN NULLABLE
  can_standalone_engine: BOOLEAN NULLABLE
  can_stop_indicator_status: BOOLEAN NULLABLE
  can_throttle_pedal_level: INTEGER NULLABLE
  can_tire_pressure_low_status: BOOLEAN NULLABLE
  can_tracker_counted_fuel_consumed: FLOAT NULLABLE
  can_tracker_counted_mileage: FLOAT NULLABLE
  can_trip_engine_motorhours: FLOAT NULLABLE
  can_trunk_status: BOOLEAN NULLABLE
  can_vehicle_battery_charging_status: BOOLEAN NULLABLE
  can_vehicle_mileage: FLOAT NULLABLE
  can_vehicle_speed: INTEGER NULLABLE
  can_warning_indicator_status: BOOLEAN NULLABLE
  can_wear_brake_pads_indicator_status: BOOLEAN NULLABLE
  can_webasto_status: BOOLEAN NULLABLE
  can_trailer_axle_lift_status_1: BOOLEAN NULLABLE
  can_trailer_axle_lift_status_2: BOOLEAN NULLABLE
  can_fuel_level: FLOAT NULLABLE
  can_engine_oil_level: FLOAT NULLABLE
}


entity "**compressor_sensor_data**" {
  asset_id: STRING NULLABLE
  --
  time: TIMESTAMP NULLABLE
  device_id: STRING NULLABLE
  created_at: TIMESTAMP NULLABLE
  client_id: STRING NULLABLE
  press_air_feed_pressure: FLOAT NULLABLE
  press_air_exhaust_temperature: FLOAT NULLABLE
  press_run_time: FLOAT NULLABLE
  press_load_time: FLOAT NULLABLE
  press_phase_a_current: FLOAT NULLABLE
  press_phase_b_current: FLOAT NULLABLE
  press_phase_c_current: FLOAT NULLABLE
  press_run_state_1: FLOAT NULLABLE
  press_run_state_2: FLOAT NULLABLE
  press_oil_filter_used_time: FLOAT NULLABLE
  press_oil_separator_used_time: FLOAT NULLABLE
  press_air_filter_used_time: FLOAT NULLABLE
  press_lube_oil_used_time: FLOAT NULLABLE
  press_lube_grease_used_time: FLOAT NULLABLE
  integration_id: STRING NULLABLE
  press_machine_status: STRING NULLABLE
  press_current_imbalance: FLOAT NULLABLE
}


entity "**general_sensor_data**"{
  asset_id : STRING NULLABLE
  --
  time : TIMESTAMP NULLABLE
  ident : STRING NULLABLE
  created_at : TIMESTAMP NULLABLE
  integration_id : STRING NULLABLE
  client_id : STRING NULLABLE
  battery_current : FLOAT NULLABLE
  battery_voltage : FLOAT NULLABLE
  gsm_signal_level : INTEGER NULLABLE
  movement_status : BOOLEAN NULLABLE
  external_powersource_voltage : FLOAT NULLABLE
  digital_input : BOOLEAN NULLABLE
  digital_output : BOOLEAN NULLABLE
}

entity "**gps_sensor_data_v2**"{
	asset_id : STRING NULLABLE
    --
	time : TIMESTAMP NULLABLE
	ident : STRING NULLABLE
	created_at : TIMESTAMP NULLABLE
	client_id : STRING NULLABLE
	position_altitude : INTEGER NULLABLE
	position_direction : INTEGER NULLABLE
	position_hdop : FLOAT NULLABLE
	position_latitude : FLOAT NULLABLE
	position_longitude : FLOAT NULLABLE
	position_pdop : FLOAT NULLABLE
	position_satellites : INTEGER NULLABLE
	position_speed : INTEGER NULLABLE
	position_valid : BOOLEAN NULLABLE
	vehicle_mileage : FLOAT NULLABLE
	integration_id : STRING NULLABLE
}

entity "**log_raw_sensor_data**"{
	time : TIMESTAMP NULLABLE
	ident : STRING NULLABLE
	data : JSON NULLABLE
	created_at : TIMESTAMP NULLABLE
	source_code : STRING NULLABLE
}

entity "**tyre_changer_data**"{
	asset_id : STRING NULLABLE
    --
	time : TIMESTAMP NULLABLE
	ident : STRING NULLABLE
	created_at : TIMESTAMP NULLABLE
	integration_id : STRING NULLABLE
	client_id : STRING NULLABLE
	current : FLOAT NULLABLE
	power : FLOAT NULLABLE
	today : FLOAT NULLABLE
	total : FLOAT NULLABLE
	voltage : FLOAT NULLABLE
	yesterday : FLOAT NULLABLE
	hm : INTEGER NULLABLE
}


entity "**tyre_sensor_data**"{
	asset_id: STRING NULLABLE	
    --
	time: TIMESTAMP NULLABLE	
	ident: STRING NULLABLE	
	created_at: TIMESTAMP NULLABLE	
	integration_id: STRING NULLABLE	
	client_id: STRING NULLABLE	
	ident_mac_address: STRING NULLABLE	
	battery_voltage: FLOAT NULLABLE	
	pressure: FLOAT NULLABLE	
	temperature: FLOAT NULLABLE
  tyre_row_position: INTEGER NULLABLE
  tyre_position: INTEGER NULLABLE
  parent_asset_id: STRING NULLABLE
}

@enduml
