package routers

import (
	"assetfindr/internal/app/integration/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterIntegrationRoutes(route *gin.Engine, integrationHandler handler.IntegrationHandler) *gin.Engine {

	adminIntegrationAccountRoute := route.Group("/v1/admin/integrations/accounts",
		middleware.TokenValidationMiddleware(),
		middleware.AdminValidationMiddleware(),
	)
	{
		adminIntegrationAccountRoute.GET("", integrationHandler.GetAdminIntegrationAccounts)
		adminIntegrationAccountRoute.POST("", integrationHandler.CreateIntegrationAccountFromAdmin)
		adminIntegrationAccountRoute.PUT("/:id/credential", integrationHandler.UpdateIntegrationAccountCredentialFromAdmin)
		adminIntegrationAccountRoute.DELETE("/:id", integrationHandler.DeleteIntegrationAccountFrimAdmin)
	}

	integrationAccountRoute := route.Group("/v1/integrations/accounts", middleware.TokenValidationMiddleware())
	{
		integrationAccountRoute.POST("", integrationHandler.CreateIntegrationAccount)
		integrationAccountRoute.POST("/validate", integrationHandler.ValidateCreateIntegrationAccount)
		integrationAccountRoute.GET("", integrationHandler.GetIntegrationAccounts)
		integrationAccountRoute.GET("/:id", integrationHandler.GetIntegrationAccount)
		integrationAccountRoute.PUT("/:id/credential", integrationHandler.UpdateIntegrationAccountCredential)
		integrationAccountRoute.PUT("/:id/status", integrationHandler.UpdateIntegrationAccountStatus)
		integrationAccountRoute.DELETE("/:id", integrationHandler.DeleteIntegrationAccount)
	}

	integrationTargetRoute := route.Group("/v1/integrations/targets", middleware.TokenValidationMiddleware())
	{
		integrationTargetRoute.GET("", integrationHandler.GetIntegrationAccountTargets)
		integrationTargetRoute.GET("/types", integrationHandler.GetIntegrationAccountTargetTypes)
	}

	integrationRoute := route.Group("/v1/integrations", middleware.TokenValidationMiddleware())
	{
		integrationRoute.POST("", integrationHandler.CreateIntegration)
		integrationRoute.POST("/validate", integrationHandler.ValidateCreateIntegration)
		integrationRoute.GET("", integrationHandler.GetIntegrations)
		integrationRoute.GET("/vehicles", integrationHandler.GetIntegrationVehicles)
		integrationRoute.GET("/trackings", integrationHandler.GetIntegrationTrackings)
		integrationRoute.GET("/:id", integrationHandler.GetIntegration)
		integrationRoute.PUT("/:id/identifier", integrationHandler.UpdateIntegrationIdentifier)
		integrationRoute.PUT("/:id/data-mapping", integrationHandler.UpdateIntegrationDataMapping)
		integrationRoute.PUT("/:id/show-data-mapping-on-platform", integrationHandler.UpdateShowDataMappingOnPlatform)
		integrationRoute.PUT("/:id/show-data-mapping-on-client", integrationHandler.UpdateShowDataMappingOnClient)
		integrationRoute.DELETE("/:id", integrationHandler.DeleteIntegration)

		integrationRoute.GET("/commands", integrationHandler.GetIntegrationCommands)
		integrationRoute.POST("/commands", integrationHandler.CreateIntegrationCommand)
	}

	adminIntegrationRoute := route.Group("/v1/admin/integrations",
		middleware.TokenValidationMiddleware(),
		// middleware.AdminValidationMiddleware(),
	)
	{
		adminIntegrationRoute.POST("", integrationHandler.CreateIntegrationFromAdmin)
		adminIntegrationRoute.GET("", integrationHandler.GetIntegrationsFromAdmin)
		adminIntegrationRoute.GET("/:id", integrationHandler.GetIntegrationsDetailsFromAdmin)
		adminIntegrationRoute.PUT("/:id/identifier", integrationHandler.UpdateIntegrationIdentifierFromAdmin)
		adminIntegrationRoute.DELETE("/:id", integrationHandler.DeleteIntegrationFromAdmin)
		adminIntegrationRoute.PUT("/:id", integrationHandler.UpdateIntegrationFromAdmin)

		adminIntegrationRoute.GET("/asset", integrationHandler.GetListAssetAdmin)
	}

	adminParameters := route.Group("/v1/admin/parameters", middleware.TokenValidationMiddleware())
	{
		adminParameters.GET("", integrationHandler.GetParametersAdmin)
	}

	adminIntegrationAlertRoute := route.Group("/v1/admin/integration-alerts",
		middleware.APITokenMiddleware,
	)
	{
		adminIntegrationAlertRoute.GET("/check-last-sensor-data-received", integrationHandler.CheckLastSensorDataReceived)
	}

	integrationAdminRoute := route.Group("/v1/admin/integrations/integrates", middleware.APITokenMiddleware)
	{
		integrationAdminRoute.POST("", integrationHandler.Integrates)
	}

	route.GET("/aol-oauth-callback", integrationHandler.GetToken)

	integrationSessionRoute := route.Group("/v1/integrations/sessions", middleware.TokenValidationMiddleware())
	{
		integrationSessionRoute.POST("/accurate", integrationHandler.UpsertIntegrationSessionAccurate)
	}

	integrationTranslogicCalibrationRoute := route.Group("/v1/integrations/translogic/calibrations", middleware.TokenValidationMiddleware())
	{
		integrationTranslogicCalibrationRoute.GET("", integrationHandler.GetTranslogicCalibrations)
		integrationTranslogicCalibrationRoute.POST("/", integrationHandler.CreateTranslogicCalibration)
		integrationTranslogicCalibrationRoute.GET("/export", integrationHandler.ExportTranslogicCalibrations)
	}

	monitoringsRoute := route.Group("/v1/integrations/monitorings", middleware.TokenValidationMiddleware())
	{
		monitoringsRoute.GET("", integrationHandler.GetIntegrationMonitorigs)
	}

	monitoringDisplayConfigRoute := route.Group("/v1/integrations/monitorings/display-configs", middleware.TokenValidationMiddleware())
	{
		monitoringDisplayConfigRoute.GET("", integrationHandler.GetMonitoringDisplayConfig)
		monitoringDisplayConfigRoute.POST("", integrationHandler.UpsertMonitoringDisplayConfig)
	}

	adminIntegrationDataMappingTemplate := route.Group("/v1/admin/integrations/data-mapping-templates",
		middleware.TokenValidationMiddleware(),
		// middleware.AdminValidationMiddleware(),
	)
	{
		adminIntegrationDataMappingTemplate.GET("", integrationHandler.GetIntegrationDataMappingTemplates)
		adminIntegrationDataMappingTemplate.POST("", integrationHandler.CreateIntegrationDataMappingTemplate)
		adminIntegrationDataMappingTemplate.PUT("/:code", integrationHandler.UpdateIntegrationDataMappingTemplate)
	}

	return route
}
