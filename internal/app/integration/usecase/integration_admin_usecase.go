package usecase

import (
	assetModel "assetfindr/internal/app/asset/models"
	geoModel "assetfindr/internal/app/geo/models"
	"assetfindr/internal/app/integration/constants"
	"assetfindr/internal/app/integration/dtos"
	"assetfindr/internal/app/integration/models"
	notificationModels "assetfindr/internal/app/notification/models"
	userModel "assetfindr/internal/app/user-identity/models"
	internalConstants "assetfindr/internal/constants"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/cryptohelpers"
	"context"
	"fmt"
	"os"
	"time"

	"github.com/jackc/pgtype"
)

func (uc *IntegrationUseCase) getClientNameMap(ctx context.Context, clientIDs []string) (map[string]string, error) {
	clients, err := uc.clientRepo.GetClients(ctx, uc.DB.DB(), userModel.ClientCondition{
		Where: userModel.ClientWhere{
			IDs: clientIDs,
		},
		Columns: []string{},
		Preload: userModel.ClientPreload{},
	})
	if err != nil {
		return nil, err
	}

	mapClientName := map[string]string{}
	for _, client := range clients {
		mapClientName[client.ID] = client.Name
	}

	return mapClientName, nil
}

func (uc *IntegrationUseCase) getAssetsMaps(ctx context.Context, assetIDs []string) (map[string]string, map[string]string, error) {
	assets, err := uc.assetRepo.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			IDs: assetIDs,
		},
		Columns: []string{},
		Preload: assetModel.AssetPreload{},
	})
	if err != nil {
		return nil, nil, err
	}

	mapAssetName := map[string]string{}
	mapAssetIdentifierID := map[string]string{}
	for _, asset := range assets {
		mapAssetName[asset.ID] = asset.Name

		identifierID := ""
		if identifierID == "" && asset.SerialNumber != "" {
			identifierID = asset.SerialNumber
		}
		if identifierID == "" && asset.ReferenceNumber != "" {
			identifierID = asset.ReferenceNumber
		}
		mapAssetIdentifierID[asset.ID] = identifierID
	}

	return mapAssetName, mapAssetIdentifierID, nil
}

func (uc *IntegrationUseCase) constructSensorDataAlertArray(duration int, integrations []models.Integration, mapClientName map[string]string, mapAssetName map[string]string, mapAssetIdentifierID map[string]string, latestIntegrationData []geoModel.LatestIntegrationData) []dtos.SensorDataAlert {
	sensorDataAlertArr := []dtos.SensorDataAlert{}
	for _, integration := range integrations {
		lastReceived := "-"
		shouldAlert := false

		var latestIntegrationMatch = geoModel.LatestIntegrationData{}
		for _, tsLatestIntegration := range latestIntegrationData {
			if integration.ID == tsLatestIntegration.IntegrationID {
				latestIntegrationMatch = tsLatestIntegration
			}
		}
		if latestIntegrationMatch.IntegrationID != "" {
			// GET TIMESCALE LATEST INTEGRATION TIME DIFF
			now := time.Now()
			diff := now.Sub(latestIntegrationMatch.UpdatedAt)
			diffInMinutes := int(diff.Minutes())
			if diffInMinutes > duration {
				// TIMESCALE LATEST INTEGRATION LAST RECEIVED MORE THAN DURATION REQ
				lastReceived = latestIntegrationMatch.UpdatedAt.Format("02 01 2006 15:04:05")
				shouldAlert = true
			}
		} else {
			shouldAlert = true
		}

		if shouldAlert {
			sensorDataAlert := dtos.SensorDataAlert{
				IntegrationName: integration.Name,
				IntegrationID:   integration.ID,
				AssetName:       mapAssetName[integration.InternalReferenceID],
				IdentifierID:    mapAssetIdentifierID[integration.InternalReferenceID],
				ClientName:      mapClientName[integration.ClientID],
				LastReceived:    lastReceived,
			}
			sensorDataAlertArr = append(sensorDataAlertArr, sensorDataAlert)
		}
	}

	return sensorDataAlertArr
}

func (uc *IntegrationUseCase) constructSensorDataAlertMailBody(sensorDataAlertArr []dtos.SensorDataAlert) string {
	mailBody := ""
	mailItemPos := 1

	mailBody += `
		<table style="border-collapse: collapse;">
			<tr>
				<th style="border: 1px solid #dddddd;">No</th>
				<th style="border: 1px solid #dddddd;">Integration Name</th>
				<th style="border: 1px solid #dddddd;">Integration ID</th>
				<th style="border: 1px solid #dddddd;">Asset Name</th>
				<th style="border: 1px solid #dddddd;">Identifier ID</th>
				<th style="border: 1px solid #dddddd;">Client Name</th>
				<th style="border: 1px solid #dddddd;">Last Received</th>
			</tr>`

	for _, sensorDataAlert := range sensorDataAlertArr {
		integrationName := helpers.StringOrDefaultIfEmpty(sensorDataAlert.IntegrationName, "-")
		integrationID := helpers.StringOrDefaultIfEmpty(sensorDataAlert.IntegrationID, "-")
		assetName := helpers.StringOrDefaultIfEmpty(sensorDataAlert.AssetName, "-")
		identifierID := helpers.StringOrDefaultIfEmpty(sensorDataAlert.IdentifierID, "-")
		clientName := helpers.StringOrDefaultIfEmpty(sensorDataAlert.ClientName, "-")
		lastReceived := helpers.StringOrDefaultIfEmpty(sensorDataAlert.LastReceived, "-")

		mailBody += fmt.Sprintf(`
				<tr>
					<td style="border: 1px solid #dddddd;">%v</td>
					<td style="border: 1px solid #dddddd;">%v</td>
					<td style="border: 1px solid #dddddd;">%v</td>
					<td style="border: 1px solid #dddddd;">%v</td>
					<td style="border: 1px solid #dddddd;">%v</td>
					<td style="border: 1px solid #dddddd;">%v</td>
					<td style="border: 1px solid #dddddd;">%v</td>
				</tr>
			`, mailItemPos, integrationName, integrationID, assetName, identifierID, clientName, lastReceived)
		mailItemPos += 1
	}

	mailBody += `
		</table>
		`

	return mailBody
}

func (uc *IntegrationUseCase) CreateIntegrationAccountFromAdmin(ctx context.Context, req dtos.AdminCreateIntegrationAccount) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	authCredential := string(req.AuthCredentialJSON.Bytes)
	if req.TargetCode != constants.INTEGRATION_TARGET_ACCURATE {
		authCredential = cryptohelpers.DefaultEncrypt(string(req.AuthCredentialJSON.Bytes))
	}

	integrationAccount := &models.IntegrationAccount{
		ModelV2: commonmodel.ModelV2{
			CreatedBy: claim.UserID,
			UpdatedBy: claim.UserID,
			ClientID:  req.ClientID,
		},
		Name:               req.Name,
		TargetCode:         req.TargetCode,
		AuthCredentialJSON: authCredential,
		StatusCode:         constants.INTEGRATION_STATUS_CODE_ACTIVE,
	}
	err = uc.integrationRepo.CreateIntegrationAccount(ctx, tX.DB(), integrationAccount)
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: integrationAccount.ID,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) GetAdminIntegrationAccounts(ctx context.Context, req dtos.AdminIntegrationAccountListReq) (*commonmodel.ListResponse, error) {
	where := models.IntegrationAccountWhere{
		ClientIDs:   req.ClientIDs,
		ShowDeleted: req.ShowDeleted,
		TargetCodes: req.TargetCodes,
	}

	count, integrationAccounts, err := uc.integrationRepo.GetIntegrationAccountList(ctx, uc.DB.DB(), models.GetIntegrationAccountListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationAccountCondition{
			Where: where,
			Preload: models.IntegrationAccountPreload{
				IntegrationTarget: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	clientIDs := []string{}
	for i := range integrationAccounts {
		clientIDs = append(clientIDs, integrationAccounts[i].ClientID)
	}

	clients, err := uc.clientRepo.GetClients(ctx, uc.DB.DB(), userModel.ClientCondition{
		Where: userModel.ClientWhere{
			IDs: clientIDs,
		},
		Columns: []string{},
		Preload: userModel.ClientPreload{},
	})
	if err != nil {
		return nil, err
	}

	mapClientName := map[string]string{}
	for i := range clients {
		mapClientName[clients[i].ID] = clients[i].Name
	}

	respData := make([]dtos.AdminIntegrationAccount, 0, len(integrationAccounts))
	for _, integrationAccount := range integrationAccounts {
		if integrationAccount.IntegrationTarget.AuthCredentialJSONTemplate.Status == pgtype.Undefined {
			integrationAccount.IntegrationTarget.AuthCredentialJSONTemplate.Status = pgtype.Null
		}
		respData = append(respData, dtos.AdminIntegrationAccount{
			IntegrationAccount: dtos.IntegrationAccount{
				ID:         integrationAccount.ID,
				Name:       integrationAccount.Name,
				TargetCode: integrationAccount.TargetCode,
				StatusCode: integrationAccount.StatusCode,
				IntegrationTarget: dtos.IntegrationTarget{
					Code:                       integrationAccount.IntegrationTarget.Code,
					Label:                      integrationAccount.IntegrationTarget.Label,
					Description:                integrationAccount.IntegrationTarget.Description,
					Type:                       integrationAccount.IntegrationTarget.Type,
					AuthCredentialJSONTemplate: integrationAccount.IntegrationTarget.AuthCredentialJSONTemplate,
				},
			},
			ClientID:   integrationAccount.ClientID,
			ClientName: mapClientName[integrationAccount.ClientID],
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *IntegrationUseCase) UpdateIntegrationAccountCredentialFromAdmin(ctx context.Context, id string, req dtos.UpdateIntegrationAccountCredential) (*commonmodel.UpdateResponse, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	account, err := uc.integrationRepo.GetIntegrationAccount(ctx, tx.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID: id,
		},
	})
	if err != nil {
		return nil, err
	}
	authCredential := string(req.AuthCredentialJSON.Bytes)

	if account.TargetCode != constants.INTEGRATION_TARGET_ACCURATE {
		authCredential = cryptohelpers.DefaultEncrypt(string(req.AuthCredentialJSON.Bytes))
	}
	err = uc.integrationRepo.UpdateIntegrationAccount(ctx, tx.DB(), id, &models.IntegrationAccount{
		AuthCredentialJSON: authCredential,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) DeleteIntegrationAccountFrimAdmin(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	integrationAccount, err := uc.integrationRepo.GetIntegrationAccount(ctx, uc.DB.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID: id,
		},
	})
	if err != nil {
		return nil, err
	}

	if integrationAccount.StatusCode == constants.INTEGRATION_STATUS_CODE_DELETED {
		return &commonmodel.DeleteResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: id,
		}, nil
	}

	updateIntegrationAccount := models.IntegrationAccount{
		StatusCode: constants.INTEGRATION_STATUS_CODE_DELETED,
	}
	err = uc.integrationRepo.UpdateIntegrationAccount(ctx, uc.DB.WithCtx(ctx).DB(), id, &updateIntegrationAccount)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *IntegrationUseCase) CreateIntegrationFromAdmin(ctx context.Context, req dtos.AdminCreateIntegration) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationAccount, err := uc.integrationRepo.GetIntegrationAccount(ctx, uc.DB.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID: req.IntegrationAccountID,
		},
	})
	if err != nil {
		return nil, err
	}

	integrationTargetType, err := uc.integrationRepo.GetIntegrationAccountTargetType(ctx, uc.DB.DB(), models.IntegrationTargetTypeCondition{
		Where: models.IntegrationTargetTypeWhere{
			Code: req.IntegrationTargetTypeCode,
		},
	})
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	integrationStatus := constants.INTEGRATION_STATUS_CODE_ACTIVE

	if req.CustomDataMapping.Status == pgtype.Undefined {
		req.CustomDataMapping.Status = pgtype.Null
	}

	integration := &models.Integration{
		ModelV2: commonmodel.ModelV2{
			CreatedBy: claim.UserID,
			UpdatedBy: claim.UserID,
			ClientID:  req.ClientID,
		},
		Name:                      req.Name,
		InternalReferenceID:       req.InternalReferenceID,
		IntegrationTargetTypeCode: req.IntegrationTargetTypeCode,
		IdentifierJSON:            req.IdentifierJSON,
		IntegrationAccountID:      req.IntegrationAccountID,
		IntegrationTargetCode:     integrationAccount.TargetCode,
		StatusCode:                integrationStatus,
		ICCID:                     req.ICCID,
		ShowDataMappingOnPlatform: req.ShowDataMappingOnPlatform,
		ShowDataMappingOnClient:   req.ShowDataMappingOnClient,
		DataMappingCode:           req.DataMappingCode,
		CustomDataMapping:         req.CustomDataMapping,
		Description:               req.Description,
	}
	err = uc.integrationRepo.CreateIntegration(ctx, tX.DB(), integration)
	if err != nil {
		return nil, err
	}

	switch integrationTargetType.IntegrationTypeCode {
	case constants.INTEGRATION_TYPE_CODE_TRACKING:
		err = uc.CreateTrackingIntegrationFromAdmin(ctx, integrationAccount, integration)
		if err != nil {
			return nil, err
		}
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: integration.ID,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) CreateTrackingIntegrationFromAdmin(ctx context.Context, integrationAccount *models.IntegrationAccount, integration *models.Integration) error {
	assetVehicle, err := uc.assetVehicleRepo.GetAssetVehicle(ctx, uc.DB.DB(), assetModel.AssetVehicleCondition{
		Where: assetModel.AssetVehicleWhere{
			AssetID: integration.InternalReferenceID,
		},
	})
	if err != nil {
		return err
	}

	firstGPSKM := 0
	switch integration.IntegrationTargetTypeCode {
	case constants.INTEGRATION_TARGET_TYPE_GPS_ID_TRACKING:
		var err error
		firstGPSKM, err = uc.GetFirstVehicleMeterFromGPSID(ctx, integrationAccount, integration)
		if err != nil {
			return err
		}
	}

	err = uc.trackingRepo.CreatePreGPSVehicleMeter(ctx, uc.DBTimeScale.DB(), &geoModel.PreGPSVehicleMeter{
		IntegrationID:  integration.ID,
		AssetID:        integration.InternalReferenceID,
		FirstVehicleKM: int(assetVehicle.VehicleKM),
		FirstGPSKM:     firstGPSKM,
		TargetCode:     constants.MapIntegrationTrackingTypeCode()[integration.IntegrationTargetTypeCode],
		ClientID:       integration.ClientID,
	})
	if err != nil {
		return err
	}
	return nil
}

func (uc *IntegrationUseCase) GetIntegrationsFromAdmin(ctx context.Context, req dtos.AdminIntegrationListReq) (*commonmodel.ListResponse, error) {
	count, integrationAccounts, err := uc.integrationRepo.GetIntegrationList(ctx, uc.DB.DB(), models.GetIntegrationListParam{
		ListRequest: req.ListRequest,
		Cond: models.IntegrationCondition{
			Where: models.IntegrationWhere{
				ClientID: req.ClientID,
			},
			Preload: models.IntegrationPreload{
				Asset: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	clientIDs := []string{}
	for i := range integrationAccounts {
		clientIDs = append(clientIDs, integrationAccounts[i].ClientID)
	}

	clients, err := uc.clientRepo.GetClients(ctx, uc.DB.DB(), userModel.ClientCondition{
		Where: userModel.ClientWhere{
			IDs: clientIDs,
		},
		Columns: []string{},
		Preload: userModel.ClientPreload{},
	})
	if err != nil {
		return nil, err
	}

	mapClientName := map[string]string{}
	for i := range clients {
		mapClientName[clients[i].ID] = clients[i].Name
	}

	respData := make([]dtos.AdminIntegration, 0, len(integrationAccounts))
	for _, integration := range integrationAccounts {
		if integration.CustomDataMapping.Status == pgtype.Undefined {
			integration.CustomDataMapping.Status = pgtype.Null
		}

		if integration.IdentifierJSON.Status == pgtype.Undefined {
			integration.IdentifierJSON.Status = pgtype.Null
		}

		respData = append(respData, dtos.AdminIntegration{
			Integration: dtos.Integration{
				ID:                        integration.ID,
				Name:                      integration.Name,
				InternalReferenceID:       integration.InternalReferenceID,
				IntegrationTargetTypeCode: integration.IntegrationTargetTypeCode,
				IdentifierJSON:            integration.IdentifierJSON,
				IntegrationAccountID:      integration.IntegrationAccountID,
				IntegrationTargetCode:     integration.IntegrationTargetCode,
				StatusCode:                integration.StatusCode,
				LastSuccessSyncTime:       integration.LastSuccessSyncTime,
				CustomDataMapping:         integration.CustomDataMapping,
				ICCID:                     integration.ICCID,
				ShowDataMappingOnPlatform: integration.ShowDataMappingOnPlatform,
				ShowDataMappingOnClient:   integration.ShowDataMappingOnClient,
				AssetDetail: dtos.Asset{
					ID:              integration.AssetDetail.ID,
					Name:            integration.AssetDetail.Name,
					SerialNumber:    integration.AssetDetail.SerialNumber,
					ReferenceNumber: integration.AssetDetail.ReferenceNumber,
				},
			},
			ClientID:   integration.ClientID,
			ClientName: mapClientName[integration.ClientID],
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *IntegrationUseCase) UpdateIntegrationIdentifierFromAdmin(ctx context.Context, id string, req dtos.UpdateIntegrationIdentifier) (*commonmodel.UpdateResponse, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()
	_, err = uc.integrationRepo.GetIntegration(ctx, tx.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID: id,
		},
		Preload: models.IntegrationPreload{
			IntegrationAccount: true,
		},
	})
	if err != nil {
		return nil, err
	}

	if req.CustomDataMapping.Status == pgtype.Undefined {
		req.CustomDataMapping.Status = pgtype.Null
	}

	integrationUpdate := &models.Integration{
		Name:                      req.Name,
		InternalReferenceID:       req.InternalReferenceID,
		IntegrationTargetTypeCode: req.IntegrationTargetTypeCode,
		IdentifierJSON:            req.IdentifierJSON,
		IntegrationAccountID:      req.IntegrationAccountID,
		IntegrationTargetCode:     req.IntegrationTargetCode,
		StatusCode:                req.StatusCode,
		ICCID:                     req.ICCID,
		ShowDataMappingOnPlatform: req.ShowDataMappingOnPlatform,
		ShowDataMappingOnClient:   req.ShowDataMappingOnClient,
		DataMappingCode:           req.DataMappingCode,
		CustomDataMapping:         req.CustomDataMapping,
		Description:               req.Description,
	}
	err = uc.integrationRepo.UpdateIntegration(ctx, tx.DB(), id, integrationUpdate)
	if err != nil {
		return nil, err
	}

	// DEPRECATE not use gps id anymore
	// switch integrationTargetType.IntegrationTypeCode {
	// case constants.INTEGRATION_TYPE_CODE_TRACKING:
	// 	uc.UpdateTrackingIntegration(ctx, &integration.IntegrationAccount, integration)
	// }

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) DeleteIntegrationFromAdmin(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	_, err := uc.integrationRepo.GetIntegration(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID: id,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.integrationRepo.DeleteIntegration(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *IntegrationUseCase) UpdateIntegrationFromAdmin(ctx context.Context, req dtos.AdminUpdateIntegration) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationAccount, err := uc.integrationRepo.GetIntegrationAccount(ctx, uc.DB.DB(), models.IntegrationAccountCondition{
		Where: models.IntegrationAccountWhere{
			ID: req.IntegrationAccountID,
		},
	})
	if err != nil {
		return nil, err
	}

	integrationTargetType, err := uc.integrationRepo.GetIntegrationAccountTargetType(ctx, uc.DB.DB(), models.IntegrationTargetTypeCondition{
		Where: models.IntegrationTargetTypeWhere{
			Code: req.IntegrationTargetTypeCode,
		},
	})
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	if req.CustomDataMapping.Status == pgtype.Undefined {
		req.CustomDataMapping.Status = pgtype.Null
	}

	integration := &models.Integration{
		ModelV2: commonmodel.ModelV2{
			CreatedBy: claim.UserID,
			UpdatedBy: claim.UserID,
			ClientID:  req.ClientID,
		},
		Name:                      req.Name,
		InternalReferenceID:       req.InternalReferenceID,
		IntegrationTargetTypeCode: req.IntegrationTargetTypeCode,
		IdentifierJSON:            req.IdentifierJSON,
		IntegrationAccountID:      req.IntegrationAccountID,
		IntegrationTargetCode:     integrationAccount.TargetCode,
		StatusCode:                req.StatusCode,
		ICCID:                     req.ICCID,
		ShowDataMappingOnPlatform: req.ShowDataMappingOnPlatform,
		ShowDataMappingOnClient:   req.ShowDataMappingOnClient,
		DataMappingCode:           req.DataMappingCode,
		Description:               req.Description,
	}
	err = uc.integrationRepo.UpdateIntegration(ctx, tX.DB(), req.IntegretionID, integration)
	if err != nil {
		return nil, err
	}

	switch integrationTargetType.IntegrationTypeCode {
	case constants.INTEGRATION_TYPE_CODE_TRACKING:
		err = uc.CreateTrackingIntegrationFromAdmin(ctx, integrationAccount, integration)
		if err != nil {
			return nil, err
		}
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: integration.ID,
		Data:        nil,
	}, nil
}

func (uc *IntegrationUseCase) GetIntegrationsDetailsFromAdmin(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integration, err := uc.integrationRepo.GetIntegration(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			ID: id,
		},
		Preload: models.IntegrationPreload{
			Asset: true,
		},
	})
	if err != nil {
		return nil, err
	}

	client, err := uc.clientRepo.GetClient(ctx, uc.DB.DB(), userModel.ClientCondition{
		Where: userModel.ClientWhere{
			ID: claim.GetLoggedInClientID(),
		},
		Columns: []string{},
		Preload: userModel.ClientPreload{},
	})
	if err != nil {
		return nil, err
	}

	if integration.CustomDataMapping.Status == pgtype.Undefined {
		integration.CustomDataMapping.Status = pgtype.Null
	}

	if integration.IdentifierJSON.Status == pgtype.Undefined {
		integration.IdentifierJSON.Status = pgtype.Null
	}

	respData := &dtos.AdminIntegration{
		Integration: dtos.Integration{
			ID:                        integration.ID,
			Name:                      integration.Name,
			InternalReferenceID:       integration.InternalReferenceID,
			IntegrationTargetTypeCode: integration.IntegrationTargetTypeCode,
			IdentifierJSON:            integration.IdentifierJSON,
			IntegrationAccountID:      integration.IntegrationAccountID,
			IntegrationTargetCode:     integration.IntegrationTargetCode,
			StatusCode:                integration.StatusCode,
			LastSuccessSyncTime:       integration.LastSuccessSyncTime,
			CustomDataMapping:         integration.CustomDataMapping,
			ICCID:                     integration.ICCID,
			ShowDataMappingOnPlatform: integration.ShowDataMappingOnPlatform,
			ShowDataMappingOnClient:   integration.ShowDataMappingOnClient,
			DataMappingCode:           integration.DataMappingCode,
			Description:               integration.Description,
			AssetDetail: dtos.Asset{
				ID:              integration.AssetDetail.ID,
				Name:            integration.AssetDetail.Name,
				SerialNumber:    integration.AssetDetail.SerialNumber,
				ReferenceNumber: integration.AssetDetail.ReferenceNumber,
			},
		},
		ClientID:   integration.ClientID,
		ClientName: client.Name,
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success get integration details",
		ReferenceID: id,
		Data:        respData,
	}, nil

}

func (uc *IntegrationUseCase) GetParametersAdmin(ctx context.Context, req commonmodel.ListRequest) (*commonmodel.ListResponse, error) {
	count, alertParams, err := uc.alertRepo.GetAlertParameterList(ctx, uc.DB.DB(), models.GetAlertParameterListParam{
		ListRequest: req,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         alertParams,
	}, nil
}

func (uc *IntegrationUseCase) GetListAssetAdmin(ctx context.Context, req dtos.AssetListAdmin) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	isViewAllAsset := claim.IsHasPermission(
		constants.ASSET_PERMISSION_CATEGORY,
		constants.ASSET_PERMISSION_VIEW_ALL_LIST_ASSET,
	)
	var nonViewAllAssetCondition *assetModel.NonViewAllAssetCondition
	if !isViewAllAsset {
		nonViewAllAssetCondition = &assetModel.NonViewAllAssetCondition{
			UserID: claim.UserID,
		}
	}

	count, partners, err := uc.assetRepo.GetAssetManagementList(ctx, uc.DB.DB(), assetModel.GetAssetListParam{
		ListRequest: req.ListRequest,
		Cond: assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ClientIDs:                req.ClientIDs,
				NonViewAllAssetCondition: nonViewAllAssetCondition,
			},
			Preload: assetModel.AssetPreload{
				// AssetCategory:          true,
				Brand: true,
				// OwnershipCategory:      true,
				// Location:               true,
				// AssetStatus:            true,
				// SubCategory:            true,
				// CustomAssetCategory:    true,
				// CustomAssetSubCategory: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	response := []dtos.GetAssetList{}
	for _, val := range partners {
		dto := dtos.GetAssetList{
			ID:              val.ID,
			AssetName:       val.Name,
			SerialNumber:    val.SerialNumber,
			ReferenceNumber: val.ReferenceNumber,
			Brand:           val.Brand.BrandName,
			BrandID:         val.Brand.ID,
		}
		response = append(response, dto)
	}
	respData := response

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *IntegrationUseCase) CheckLastSensorDataReceived(ctx context.Context, req dtos.AdminIntegrationCheckLastSensorDataReceivedReq) (commonmodel.DetailResponse, error) {
	var checkLastSensorDataReceivedResponse commonmodel.DetailResponse

	duration := req.Duration
	if duration <= 0 {
		duration = 60
	}

	// INTEGRATION LIST
	integrations, err := uc.integrationRepo.GetIntegrations(ctx, uc.DB.DB(), models.IntegrationCondition{
		Where: models.IntegrationWhere{
			Status: constants.INTEGRATION_STATUS_CODE_ACTIVE,
			IntegrationTargetTypeCodes: []string{
				constants.INTEGRATION_TARGET_TYPE_FLESPI_TRACKING,
				constants.INTEGRATION_TARGET_TYPE_SENDQUIP_GATEWAY,
				constants.INTEGRATION_TARGET_TYPE_FLESPI_GATEWAY,
			},
		},
	})
	if err != nil {
		return checkLastSensorDataReceivedResponse, err
	}

	// REF AND CLIENT IDS
	assetIDs := []string{}
	clientIDs := []string{}
	for _, integration := range integrations {
		assetIDs = append(assetIDs, integration.InternalReferenceID)
		clientIDs = append(clientIDs, integration.ClientID)
	}

	// MAP CLIENT
	mapClientName, err := uc.getClientNameMap(ctx, clientIDs)
	if err != nil {
		commonlogger.Errorf("failed get client name map: %v", err)
		return checkLastSensorDataReceivedResponse, err
	}

	// MAP ASSET
	mapAssetName, mapAssetIdentifierID, err := uc.getAssetsMaps(ctx, assetIDs)
	if err != nil {
		commonlogger.Errorf("failed get asset name map: %v", err)
		return checkLastSensorDataReceivedResponse, err
	}

	// GET TIMESCALE DB LATEST INTEGRATION DATA
	latestIntegrationData, err := uc.trackingRepo.GetLatestIntegrationDataList(ctx, uc.DBTimeScale.DB())
	if err != nil {
		commonlogger.Errorf("failed get timescale latest integration data: %v", err)
		return checkLastSensorDataReceivedResponse, err
	}

	// CONSTRUCT ALERT
	// CONSTRUCT SENSOR DATA
	sensorDataAlertArr := uc.constructSensorDataAlertArray(duration, integrations, mapClientName, mapAssetName, mapAssetIdentifierID, latestIntegrationData)
	// CONSTRUCT MAIL
	if len(sensorDataAlertArr) > 0 {
		mailSubject := fmt.Sprintf("Alert: No Sensor Data Received for Over %v Minutes", duration)

		mailDestinations := []string{"<EMAIL>"}
		if os.Getenv(internalConstants.ENV_APP_ENV) != "production" {
			mailDestinations = []string{"<EMAIL>"}
		}

		mailBody := uc.constructSensorDataAlertMailBody(sensorDataAlertArr)

		if mailBody != "" {
			err := uc.emailRepository.SendBasicEmail(ctx, notificationModels.BasicEmailParam{
				Destination: notificationModels.EmailDestination{
					ToAddresses: mailDestinations,
				},
				Subject:  mailSubject,
				MainBody: mailBody,
			})
			if err != nil {
				commonlogger.Warnf("failed to send email notif %v", err)
			}
		}
	}

	checkLastSensorDataReceivedResponse = commonmodel.DetailResponse{
		Success:     true,
		Message:     "Admin Integrations Check Last Sensor Data Received",
		ReferenceID: "",
		Data:        sensorDataAlertArr,
	}

	return checkLastSensorDataReceivedResponse, nil
}
