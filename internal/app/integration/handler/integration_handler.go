package handler

import (
	"assetfindr/internal/app/integration/dtos"
	"assetfindr/internal/app/integration/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonmodel"
	"net/http"

	"github.com/gin-gonic/gin"
)

type IntegrationHandler struct {
	integrationUsecase usecase.IntegrationUseCase
}

func NewIntegrationHandler(integrationUsecase usecase.IntegrationUseCase) IntegrationHandler {
	return IntegrationHandler{
		integrationUsecase: integrationUsecase,
	}
}

func (h *IntegrationHandler) GetIntegrationAccounts(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationAccountListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.GetIntegrationAccounts(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.<PERSON>(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.<PERSON>(http.StatusOK, resp)
}

func (h *IntegrationHandler) GetIntegrationAccountTargets(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationTargetListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.GetIntegrationTargets(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) GetIntegrationAccountTargetTypes(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationTargetTypeListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.GetIntegrationTargetTypes(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) GetIntegrationAccount(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	resp, err := h.integrationUsecase.GetIntegrationAccount(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *IntegrationHandler) CreateIntegrationAccount(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateIntegrationAccount
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.CreateIntegrationAccount(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) ValidateCreateIntegrationAccount(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateIntegrationAccount
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = h.integrationUsecase.ValidateCreateIntegrationAccount(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	})
}

func (h *IntegrationHandler) UpdateIntegrationAccountCredential(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.UpdateIntegrationAccountCredential
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.UpdateIntegrationAccountCredential(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) UpdateIntegrationAccountStatus(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req struct {
		StatusCode string `json:"status_code"`
	}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.UpdateIntegrationAccountStatus(ctx, id, req.StatusCode)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) DeleteIntegrationAccount(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.integrationUsecase.DeleteIntegrationAccount(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) GetIntegrations(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.GetIntegrations(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) GetIntegrationTrackings(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.GetIntegrationTrackings(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) CreateIntegration(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateIntegration
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.CreateIntegration(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) UpdateIntegrationIdentifier(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.UpdateIntegrationIdentifier
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.UpdateIntegrationIdentifier(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) UpdateIntegrationDataMapping(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.UpdateIntegrationDataMapping
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.UpdateIntegrationDataMapping(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) UpdateShowDataMappingOnPlatform(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.UpdateIntegrationShowDataMappingOnPlatform
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.UpdateShowDataMappingOnPlatform(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) UpdateShowDataMappingOnClient(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.UpdateIntegrationShowDataMappingOnClient
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.UpdateShowDataMappingOnClient(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) ValidateCreateIntegration(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateIntegration
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = h.integrationUsecase.ValidateCreateIntegration(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	})
}

func (h *IntegrationHandler) GetIntegration(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	resp, err := h.integrationUsecase.GetIntegration(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) DeleteIntegration(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.integrationUsecase.DeleteIntegration(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) Integrates(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.Integrates
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.Integrates(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) GetIntegrationVehicles(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.GetIntegrationVehicles(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) UpsertIntegrationSessionAccurate(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.UpsertIntegrationSessionAccurateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.UpsertIntegrationSessionAccurate(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) GetTranslogicCalibrations(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationTranslogicCalibrationListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.GetIntegrationTranslogicCalibrations(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) CreateTranslogicCalibration(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateTranslogicCalibration
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.CreateTranslogicCalibration(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *IntegrationHandler) GetIntegrationMonitorigs(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationMonitoringListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.GetIntegrationMonitorigs(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) ExportTranslogicCalibrations(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationTranslogicCalibrationListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.integrationUsecase.ExportIntegrationTranslogicCalibrations(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) GetIntegrationCommands(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationCommandListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.GetIntegrationCommands(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *IntegrationHandler) CreateIntegrationCommand(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.IntegrationCommandReq{}
	err := c.BindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.integrationUsecase.CreateIntegrationCommand(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
