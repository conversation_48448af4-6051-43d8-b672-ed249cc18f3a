package dtos

import (
	"assetfindr/pkg/common/commonmodel"
)

type AdminIntegrationAccountListReq struct {
	commonmodel.ListRequest
	TargetCodes      []string `form:"target_codes"`
	ClientIDs        []string `form:"client_ids"`
	IsIncludeGeneral bool     `form:"is_include_general"`
	ShowDeleted      bool     `form:"show_deleted"`
}

type AdminIntegrationAccount struct {
	IntegrationAccount
	ClientID   string `json:"client_id"`
	ClientName string `json:"client_name"`
}

type AdminCreateIntegrationAccount struct {
	CreateIntegrationAccount
	ClientID string `json:"client_id"`
}

type AdminCreateIntegration struct {
	CreateIntegration
	ClientID string `json:"client_id"`
}

type AdminUpdateIntegration struct {
	CreateIntegration
	ClientID      string `json:"client_id"`
	IntegretionID string
	StatusCode    string `json:"status_code"`
}

type AdminIntegrationListReq struct {
	commonmodel.ListRequest
	ClientID string `form:"client_id"`
}

type AdminIntegration struct {
	Integration
	ClientID   string `json:"client_id"`
	ClientName string `json:"client_name"`
}

type AdminIntegrationCheckLastSensorDataReceivedReq struct {
	Duration int `form:"duration"`
}

type SensorDataAlert struct {
	IntegrationName string `json:"integration_name"`
	IntegrationID   string `json:"integration_id"`
	AssetName       string `json:"asset_name"`
	IdentifierID    string `json:"identifier_id"`
	ClientName      string `json:"client_name"`
	LastReceived    string `json:"last_received"`
}

type AssetListAdmin struct {
	commonmodel.ListRequest
	ClientIDs []string `form:"client_id"`
}
