package models

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type IntegrationAccount struct {
	commonmodel.ModelV2
	Name               string
	TargetCode         string
	AuthCredentialJSON string `gorm:"column:auth_credential_json"`
	StatusCode         string
	IntegrationTarget  IntegrationTarget `gorm:"foreignKey:TargetCode"`
}

func (ia *IntegrationAccount) BeforeCreate(db *gorm.DB) error {
	ia.SetUUID("ina")
	ia.ModelV2.BeforeCreate(db)
	return nil
}

func (ia *IntegrationAccount) BeforeUpdate(db *gorm.DB) error {
	ia.ModelV2.BeforeUpdate(db)
	return nil
}

func (IntegrationAccount) TableName() string {
	return "ins_integration_account"
}

type IntegrationTarget struct {
	Code                       string       `gorm:"primaryKey" json:"code"`
	Label                      string       `gorm:"not null" json:"label"`
	Description                string       `gorm:"not null" json:"description"`
	Type                       string       `gorm:"foreignKey:Code" json:"-"`
	AuthCredentialJSONTemplate pgtype.JSONB `gorm:"column:auth_credential_json_template;type:jsonb"`
}

func (IntegrationTarget) TableName() string {
	return "ins_INTEGRATION_TARGET"
}

type IntegrationAccountStatus struct {
	Code        string `gorm:"primaryKey" json:"code"`
	Label       string `gorm:"not null" json:"label"`
	Description string `gorm:"not null" json:"description"`
}

func (IntegrationAccountStatus) TableName() string {
	return "ins_INTEGRATION_ACCOUNT_STATUSES"
}

type IntegrationStatus struct {
	Code        string `gorm:"primaryKey" json:"code"`
	Label       string `gorm:"not null" json:"label"`
	Description string `gorm:"not null" json:"description"`
}

func (IntegrationStatus) TableName() string {
	return "ins_INTEGRATION_STATUSES"
}

type IntegrationType struct {
	Code        string `gorm:"primaryKey" json:"code"`
	Label       string `gorm:"not null" json:"label"`
	Description string `gorm:"not null" json:"description"`
}

func (IntegrationType) TableName() string {
	return "ins_INTEGRATION_TYPE"
}

type Integration struct {
	commonmodel.ModelV2
	Name                       string
	InternalReferenceID        string
	IntegrationTargetTypeCode  string
	IntegrationTargetType      IntegrationTargetType
	IdentifierJSON             pgtype.JSONB `gorm:"type:jsonb"`
	IntegrationAccountID       string
	IntegrationTargetCode      string
	ICCID                      string         `gorm:"column:iccid"`
	ShowDataMappingOnPlatform  pq.StringArray `json:"show_data_mapping_on_platform" gorm:"column:show_data_mapping_on_platform;type:varchar(255)[]"`
	ShowDataMappingOnClient    pq.StringArray `json:"show_data_mapping_on_client" gorm:"column:show_data_mapping_on_client;type:varchar(255)[]"`
	StatusCode                 string
	LastSuccessSyncTime        time.Time
	DataMappingCode            string
	CustomDataMapping          pgtype.JSONB `gorm:"type:jsonb"`
	MachineStatusCode          null.String
	MachineStatusLastUpdatedAt null.Time
	IsEnableCommand            bool
	DataMapping                IntegrationDataMappingTemplate `gorm:"foreignKey:DataMappingCode;references:Code"`
	IntegrationAccount         IntegrationAccount
	Description                string

	AssetDetail models.Asset `gorm:"foreignKey:InternalReferenceID;references:ID"`
}

func (Integration) TableName() string {
	return "ins_integrations"
}

func (ia *Integration) BeforeCreate(db *gorm.DB) error {
	ia.SetUUID("ini")
	ia.ModelV2.BeforeCreate(db)
	return nil
}

func (ia *Integration) BeforeUpdate(db *gorm.DB) error {
	ia.ModelV2.BeforeUpdate(db)
	return nil
}

type IntegrationTargetType struct {
	Code                   string `gorm:"primaryKey"`
	Label                  string
	Description            string
	IntegrationTypeCode    string
	IntegrationTargetCode  string
	IdentifierJSONTemplate pgtype.JSONB `gorm:"type:jsonb"`
}

func (IntegrationTargetType) TableName() string {
	return "ins_INTEGRATION_TARGET_TYPE"
}

type IntegrationAccountWhere struct {
	ID                string
	TargetCode        string
	TargetCodes       []string
	ClientID          string
	ClientIDs         []string
	ClientIDOrGeneral string
	ShowDeleted       bool
	WithOrmDeleted    bool
}

type IntegrationAccountPreload struct {
	IntegrationTarget bool
}

type IntegrationAccountCondition struct {
	Where       IntegrationAccountWhere
	Preload     IntegrationAccountPreload
	Columns     []string
	IsForUpdate bool
}

type GetIntegrationAccountListParam struct {
	commonmodel.ListRequest
	Cond IntegrationAccountCondition
}

type IntegrationTargetWhere struct {
}

type IntegrationTargetCondition struct {
	Where       IntegrationTargetWhere
	Columns     []string
	IsForUpdate bool
}

type GetIntegrationTargetListParam struct {
	commonmodel.ListRequest
	Cond IntegrationTargetCondition
}

type IntegrationWhere struct {
	ID                         string
	IntegrationTargetTypeCode  string
	IntegrationTargetTypeCodes []string
	Status                     string
	IntegrationTypeCode        string
	InternalReferenceID        string
	InternalReferenceIDs       []string
	IdentifierJSON             map[string]string
	ClientID                   string
	WithOrmDeleted             bool
	HideOnMonitoring           bool
	IsEnableCommand            bool
	ICCIDs                     []string
}

type IntegrationPreload struct {
	IntegrationAccount       bool
	IntegrationTargetType    bool
	IntegrationAccountTarget bool
	DataMapping              bool
	Asset                    bool
}

type IntegrationCondition struct {
	Where       IntegrationWhere
	Preload     IntegrationPreload
	Columns     []string
	IsForUpdate bool
}

type GetIntegrationListParam struct {
	commonmodel.ListRequest
	Cond IntegrationCondition
}

type IntegrationTargetTypeWhere struct {
	Code     string
	TypeCode string
}

type IntegrationTargetTypeCondition struct {
	Where       IntegrationTargetTypeWhere
	Columns     []string
	IsForUpdate bool
}

type GetIntegrationTargetTypeListParam struct {
	commonmodel.ListRequest
	Cond IntegrationTargetTypeCondition
}

type IntegrationTranslogicCalibration struct {
	commonmodel.ModelV2
	Number                  string
	StandardTreadDepthValue float64
	DeviceID                string  `gorm:"column:device_id"`
	IdleValue               float64 `gorm:"column:idle_value"`
	TreadDepthValue         float64 `gorm:"column:tread_depth_value"`
	PressureValue           float64 `gorm:"column:pressure_value"`
}

func (itc *IntegrationTranslogicCalibration) BeforeCreate(db *gorm.DB) error {
	itc.SetUUID("itc")
	itc.ModelV2.BeforeCreate(db)
	return nil
}

func (itc *IntegrationTranslogicCalibration) BeforeUpdate(db *gorm.DB) error {
	itc.ModelV2.BeforeUpdate(db)
	return nil
}

func (IntegrationTranslogicCalibration) TableName() string {
	return "ins_translogic_calibration"
}

type GetIntegrationTranslogicCalibrationListParam struct {
	commonmodel.ListRequest
	Cond IntegrationTranslogicCalibrationCondition
}

type IntegrationTranslogicCalibrationCondition struct {
	Where   IntegrationTranslogicCalibrationWhere
	Columns []string
}

type IntegrationTranslogicCalibrationWhere struct {
	ID       string
	ClientID string
}

type IntegrationCommand struct {
	commonmodel.ModelV2
	CommandTypeCode string `json:"command_type_code"`
	StatusCode      string `json:"status_code"`
	IntegrationId   string `json:"integration_id"`

	IntegrationCommandStatus IntegrationCommandStatus `gorm:"foreignKey:StatusCode;references:Code"`
	IntegrationCommandType   IntegrationCommandType   `gorm:"foreignKey:CommandTypeCode;references:Code"`
}

func (IntegrationCommand) TableName() string {
	return "ins_integration_commands"
}

func (itc *IntegrationCommand) BeforeCreate(db *gorm.DB) error {
	itc.SetUUID("cmd")
	itc.ModelV2.BeforeCreate(db)
	return nil
}

func (itc *IntegrationCommand) BeforeUpdate(db *gorm.DB) error {
	itc.ModelV2.BeforeUpdate(db)
	return nil
}

type IntegrationCommandsListParam struct {
	commonmodel.ListRequest
	Cond IntegrationCommandsCondition
}

type IntegrationCommandsCondition struct {
	Where   IntegrationCommandsWhere
	Columns []string
	Preload IntegrationCommandsPreload
}

type IntegrationCommandsPreload struct {
	IntegrationCommandStatus bool
	IntegrationCommandType   bool
}
type IntegrationCommandsWhere struct {
	ID       string
	ClientID string
}

type IntegrationCommandStatus struct {
	Code        string `json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

func (IntegrationCommandStatus) TableName() string {
	return "ins_INTEGRATION_COMMANDS_STATUSES"
}

type IntegrationCommandType struct {
	Code        string `json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

func (IntegrationCommandType) TableName() string {
	return "ins_INTEGRATION_COMMANDS_TYPES"
}
