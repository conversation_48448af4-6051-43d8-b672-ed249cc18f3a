package usecase

import (
	"assetfindr/internal/app/approval/constants"
	"assetfindr/internal/app/approval/dtos"
	"assetfindr/internal/app/approval/models"
	"assetfindr/internal/app/approval/repository"
	assetConstants "assetfindr/internal/app/asset/constants"
	assetModel "assetfindr/internal/app/asset/models"
	assetRepository "assetfindr/internal/app/asset/repository"
	financeUsecase "assetfindr/internal/app/finance/usecase"
	packageConstants "assetfindr/internal/app/inventory/constants"
	inventoryModel "assetfindr/internal/app/inventory/models"
	packageRepository "assetfindr/internal/app/inventory/repository"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	internalConstants "assetfindr/internal/constants"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"fmt"
	"html/template"
	"os"
	"strings"
	"time"

	"github.com/lib/pq"
)

type ApprovalUseCase struct {
	DB                           database.DBUsecase
	ApprovalRepository           repository.ApprovalRepository
	AssetRepository              assetRepository.AssetRepository
	assetTyreRepo                assetRepository.AssetTyreRepository
	UserRepository               userIdentityRepository.UserRepository
	AssetStatusRequestRepository assetRepository.AssetStatusRequestRepository
	BonusPenaltyRepository       assetRepository.BonusPenaltyRepository
	AssetLinkedRepository        assetRepository.AssetLinkedRepository
	FinanceUsecase               financeUsecase.FinanceUseCase
	notifUseCase                 *notificationUsecase.NotificationUseCase
	packageRepository            packageRepository.PackageRepository
}
type ApprovalUseCaseInterface interface {
	CreateApprovalRequest(ctx context.Context, dB database.DBI, approvalRequest *models.ApprovalRequest) error
	GetApprovalRequest(ctx context.Context, assetID string, req commonmodel.ListRequest) (commonmodel.ListResponse, error)
	GetApprovalByID(ctx context.Context, id string) (*models.Approval, error)
	RejectApproval(ctx context.Context, id string, notes string) error
	ApproveApproval(ctx context.Context, id string, notes string) error
}

func NewApprovalUseCase(
	DB database.DBUsecase,
	approvalRepo repository.ApprovalRepository,
	assetRepo assetRepository.AssetRepository,
	userRepository userIdentityRepository.UserRepository,
	assetStatusRequest assetRepository.AssetStatusRequestRepository,
	bonusPenaltyRepository assetRepository.BonusPenaltyRepository,
	assetLinkedRepository assetRepository.AssetLinkedRepository,
	financeUsecase financeUsecase.FinanceUseCase,
	packageRepo packageRepository.PackageRepository,
	assetTyreRepo assetRepository.AssetTyreRepository,
) ApprovalUseCase {
	return ApprovalUseCase{
		DB:                           DB,
		ApprovalRepository:           approvalRepo,
		AssetRepository:              assetRepo,
		UserRepository:               userRepository,
		AssetStatusRequestRepository: assetStatusRequest,
		BonusPenaltyRepository:       bonusPenaltyRepository,
		AssetLinkedRepository:        assetLinkedRepository,
		FinanceUsecase:               financeUsecase,
		packageRepository:            packageRepo,
		assetTyreRepo:                assetTyreRepo,
	}
}

func (uc *ApprovalUseCase) SetNotifUseCase(notifUseCase notificationUsecase.NotificationUseCase) {
	uc.notifUseCase = &notifUseCase
}

func (uc *ApprovalUseCase) getRedirectLink(approval *models.Approval) string {
	apiURL := internalConstants.API_STAGING_URL
	if os.Getenv(internalConstants.ENV_APP_ENV) == "production" {
		apiURL = internalConstants.API_URL
	}

	return fmt.Sprintf("http://%s/v1/redirects?type=%s&ref=%s&client_id=%s", apiURL, notifConstants.REDIRECT_TYPE_APPROVAL, approval.ApprovalRequestID, approval.ClientID)
}

func convertNewlinesToHTML(text string) string {
	return strings.ReplaceAll(text, "\n", "<br>")
}

func (uc *ApprovalUseCase) notifyStatusApproveRejectApproval(
	ctx context.Context,
	approval *models.Approval,
) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		commonlogger.Warnf("error get claim on status approve reject approval", err)
		return
	}

	requester, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: approval.CreatedBy,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify status approve reject approval", err)
		return
	}

	updatedby, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: approval.ApprovalUserID,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify status approve reject approval", err)
		return
	}

	href := uc.notifUseCase.GenerateTargetURL(ctx, claim.GetName(), notifConstants.DESTINATION_TYPE_APPROVAL, approval.ID)
	title, _ := tmplhelpers.ParseStringTemplate(
		"Approval {{.Code}} was {{.Status}}",
		struct{ Code, Status string }{
			Code:   constants.GetApprovalSourceLabel(approval.UserPermissionCode),
			Status: approval.StatusCode,
		})

	descriptionHTML := convertNewlinesToHTML(approval.ApprovalRequest.Description)

	body, _ := tmplhelpers.ParseStringTemplate(
		`Approval was {{.Status}}, Type: {{.Type}}, Description: {{.Description}}, Request Note: {{.RequestNote}}, 	Updated By: {{.UpdatedBy}}, Requested By: {{.RequestedBy}},	<a href = "{{.RedirectLink}}"><button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">View Approval</button></a><br>`,
		struct {
			Status, Type, RequestNote, UpdatedBy, RequestedBy string
			RedirectLink                                      template.URL
			Description                                       template.HTML
		}{
			Status:       approval.StatusCode,
			Type:         constants.GetApprovalSourceLabel(approval.UserPermissionCode),
			Description:  template.HTML(descriptionHTML),
			RequestNote:  approval.ApprovalRequest.RequestNotes,
			UpdatedBy:    updatedby.GetName(),
			RequestedBy:  requester.GetName(),
			RedirectLink: template.URL(href),
		})

	notifItems := make([]notificationDtos.CreateNotificationItem, 0, len(approval.ApprovalRequest.ReferenceIDs)*2)

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            approval.CreatedBy,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_APPROVAL,
		SourceReferenceID: approval.ID,
		TargetReferenceID: "",
		TargetURL:         href,
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          approval.ClientID,
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: title,
		},
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_APPROVAL,
		ReferenceValue:  approval.ID,
	}

	if len(approval.ApprovalRequest.ReferenceIDs) > 0 {
		notifItem.TargetReferenceID = approval.ApprovalRequest.ReferenceIDs[0]
	}

	notifItems = append(notifItems, notifItem) // add notifItem for CreatedBy

	// second notification for ApprovalUserID (if different)
	if approval.CreatedBy != approval.ApprovalUserID {
		notifItem.UserID = approval.ApprovalUserID
		notifItems = append(notifItems, notifItem)
	}

	if len(notifItems) > 0 {
		_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
			Items:           notifItems,
			SendToEmail:     true,
			SendToPushNotif: true,
		})
	}

}

func (uc *ApprovalUseCase) validateApproval(ctx context.Context, id string) (*string, pq.StringArray, *string, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, nil, nil, err
	}
	approval, err := uc.ApprovalRepository.GetApprovalByID(ctx, uc.DB.DB(), models.ApprovalRequestCondition{
		Where: models.ApprovalRequestWhere{
			ClientID:   claim.GetLoggedInClientID(),
			ApprovalID: id,
		},
	})
	if err != nil {
		return nil, nil, nil, err
	}
	if approval == nil {
		return nil, nil, nil, errorhandler.ErrDataNotFound("approval")
	}
	if approval.StatusCode != constants.APPROVAL_STATUS_PENDING {
		return nil, nil, nil, errorhandler.ErrBadRequest("this approval already accepted or rejected")
	}
	switch approval.ApprovalRequest.SourceCode {
	case constants.APPROVAL_SOURCE_SCRAP_TYRE:
		return &approval.ApprovalRequestID, approval.ApprovalRequest.ReferenceIDs, &constants.APPROVAL_SOURCE_SCRAP_TYRE, nil
	case constants.APPROVAL_SOURCE_DISPOSE_TYRE:
		return &approval.ApprovalRequestID, approval.ApprovalRequest.ReferenceIDs, &constants.APPROVAL_SOURCE_DISPOSE_TYRE, nil
	case constants.APPROVAL_SOURCE_BONUS_PENALTY_TYRE:
		return &approval.ApprovalRequestID, approval.ApprovalRequest.ReferenceIDs, &constants.APPROVAL_SOURCE_BONUS_PENALTY_TYRE, nil
	case constants.APPROVAL_SOURCE_ASSET_MEMBERSHIP:
		return &approval.ApprovalRequestID, approval.ApprovalRequest.ReferenceIDs, &constants.APPROVAL_SOURCE_ASSET_MEMBERSHIP, nil
	case constants.APPROVAL_SOURCE_CREATE_NEW_ASSET:
		return &approval.ApprovalRequestID, approval.ApprovalRequest.ReferenceIDs, &constants.APPROVAL_SOURCE_CREATE_NEW_ASSET, nil
	default:
		return nil, nil, nil, errorhandler.ErrBadRequest("unknown approval source code")
	}
}

func (uc *ApprovalUseCase) updateApprovalAndRequest(ctx context.Context, id string, notes string, approvalRequestID string, statusCode string, approvalReferenceIDs pq.StringArray, approvalSource string) error {
	assetStatus := assetConstants.ASSET_STATUS_CODE_DISPOSED
	if approvalSource == constants.APPROVAL_SOURCE_SCRAP_TYRE {
		assetStatus = assetConstants.ASSET_STATUS_CODE_SCRAPED
	}
	if statusCode == constants.APPROVAL_STATUS_REJECTED {
		assetStatus = assetConstants.ASSET_STATUS_CODE_IN_STOCK
	}
	if statusCode == constants.APPROVAL_STATUS_REJECTED && approvalSource == constants.APPROVAL_SOURCE_DISPOSE_TYRE {
		assetStatus = assetConstants.ASSET_STATUS_CODE_SCRAPED
	}
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return err
	}
	defer func() {
		tx.Rollback()
	}()
	//for now 1 approval will update approval request and asset id status code
	err = uc.ApprovalRepository.UpdateApprovalRequestStatusCode(ctx, tx.DB(), approvalRequestID, statusCode)
	if err != nil {
		return err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	err = uc.ApprovalRepository.UpdateApprovalOnAction(ctx, tx.DB(), id, statusCode, notes, claim.UserID)
	if err != nil {
		return err
	}
	switch approvalSource {
	case constants.APPROVAL_SOURCE_DISPOSE_TYRE, constants.APPROVAL_SOURCE_SCRAP_TYRE:
		assetStatusRequests, err := uc.AssetStatusRequestRepository.GetAssetStatusRequests(ctx, tx.DB(), assetModel.AssetStatusRequestCondition{
			Where: assetModel.AssetStatusRequestWhere{
				ClientID: claim.GetLoggedInClientID(),
				IDs:      approvalReferenceIDs,
			},
		})
		if err != nil {
			return err
		}
		newASR := assetModel.AssetStatusRequest{}
		newASR.StatusCode = statusCode
		newASR.UpdatedBy = claim.UserID
		err = uc.AssetStatusRequestRepository.UpdateAssetstatusRequest(ctx, tx.DB(), approvalReferenceIDs[0], &newASR)
		if err != nil {
			return err
		}

		assetIDs := make([]string, 0, len(assetStatusRequests))
		for i := range assetStatusRequests {
			assetIDs = append(assetIDs, assetStatusRequests[i].AssetID)
		}
		err = uc.AssetRepository.UpdateAssetStatusByIDs(ctx, tx.DB(), assetIDs, assetStatus)
		if err != nil {
			return err
		}

		if statusCode == assetConstants.ASSET_STATUS_CODE_IN_STOCK {
			err = uc.assetTyreRepo.UpdateAssetTyreByIDs(ctx, tx.DB(), assetIDs, &assetModel.AssetTyre{
				LastStockDate: time.Now().In(time.UTC),
			})
			if err != nil {
				return err
			}
		}

		// Resell Cost is excluded from finance

	case constants.APPROVAL_SOURCE_BONUS_PENALTY_TYRE:
		bonusPenalty, err := uc.BonusPenaltyRepository.GetBonusPenalty(ctx, tx.DB(), assetModel.BonusPenaltyCondition{
			Where: assetModel.BonusPenaltyWhere{
				ClientID: claim.GetLoggedInClientID(),
				ID:       approvalReferenceIDs[0],
			},
		})
		if err != nil {
			return err
		}
		newBP := assetModel.BonusPenalty{}
		newBP.StatusCode = statusCode
		newBP.UpdatedBy = claim.UserID
		newBP.ApprovalUserID = claim.UserID
		err = uc.BonusPenaltyRepository.UpdateBonusPenalty(ctx, tx.DB(), bonusPenalty.ID, &newBP)
		if err != nil {
			return err
		}
		if statusCode == constants.APPROVAL_STATUS_ACCEPTED {
			bonusPenaltyItems, err := uc.BonusPenaltyRepository.GetBonusPenaltyItems(ctx, tx.DB(), assetModel.BonusPenaltyItemCondition{
				Where: assetModel.BonusPenaltyItemWhere{
					BonusPenaltyID: approvalReferenceIDs[0],
					ClientID:       claim.GetLoggedInClientID(),
				},
			})
			if err != nil {
				return err
			}
			needUpdateAssetLinked := []assetModel.AssetLinked{}
			for _, val := range bonusPenaltyItems {
				linkedAssets, err := uc.AssetLinkedRepository.GetAssetLinkeds(ctx, tx.DB(), assetModel.AssetLinkedCondition{
					Where: assetModel.AssetLinkedWhere{
						ChildAssetID:  val.AssetTyreID,
						ParentAssetID: bonusPenalty.AssetVehicleID,
						ClientID:      claim.GetLoggedInClientID(),
						WithUnlinked:  true,
					},
				})
				if err != nil {
					return err
				}
				needUpdateAssetLinked = append(needUpdateAssetLinked, linkedAssets...)
			}
			assetLinedsIds := []string{}
			for _, val := range needUpdateAssetLinked {
				assetLinedsIds = append(assetLinedsIds, val.ID)
			}
			err = uc.AssetLinkedRepository.UpdateClaimBonusPenaltyByIDs(ctx, tx.DB(), assetLinedsIds, true)
			if err != nil {
				return err
			}
		}
	case constants.APPROVAL_SOURCE_ASSET_MEMBERSHIP:
		for _, approvalReferenceID := range approvalReferenceIDs {
			membership, err := uc.packageRepository.GetMembership(ctx, tx.DB(), inventoryModel.MembershipCondition{
				Where: inventoryModel.MembershipWhere{
					ClientID: claim.GetLoggedInClientID(),
					ID:       approvalReferenceID,
				},
			})

			if err != nil {
				return err
			}

			if statusCode == constants.APPROVAL_STATUS_ACCEPTED {
				membership.StatusCode = packageConstants.MEMBERSHIP_STATUS_CODE_ACTIVE
			}
			if statusCode == constants.APPROVAL_STATUS_REJECTED {
				membership.StatusCode = packageConstants.MEMBERSHIP_STATUS_CODE_REJECTED
			}

			err = uc.packageRepository.UpdateMembershipStatusCode(ctx, tx.DB(), approvalReferenceID, claim.GetLoggedInClientID(), membership)

			if err != nil {
				return err
			}
		}
	}
	err = tx.Commit()
	if err != nil {
		commonlogger.Errorf("Error update approval when commit to database: %v\n", err)
		return err
	}
	return err
}

func (uc *ApprovalUseCase) RejectApproval(ctx context.Context, id string, notes string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}
	approvalRequestID, approvalReferenceIDs, approvalSource, err := uc.validateApproval(ctx, id)
	if err != nil {
		return err
	}
	statusCode := constants.APPROVAL_STATUS_REJECTED
	err = uc.updateApprovalAndRequest(ctx, id, notes, *approvalRequestID, statusCode, approvalReferenceIDs, *approvalSource)
	if err != nil {
		return err
	}
	approval, err := uc.ApprovalRepository.GetApprovalByID(ctx, uc.DB.DB(), models.ApprovalRequestCondition{
		Where: models.ApprovalRequestWhere{
			ClientID:   claim.GetLoggedInClientID(),
			ApprovalID: id,
		},
	})

	if err != nil {
		return err
	}
	go uc.notifyStatusApproveRejectApproval(contexthelpers.WithoutCancel(ctx), approval)

	return err
}

func (uc *ApprovalUseCase) ApproveApproval(ctx context.Context, id string, notes string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}
	approvalRequestID, approvalReferenceIDs, approvalSource, err := uc.validateApproval(ctx, id)
	if err != nil {
		return err
	}
	statusCode := constants.APPROVAL_STATUS_ACCEPTED
	err = uc.updateApprovalAndRequest(ctx, id, notes, *approvalRequestID, statusCode, approvalReferenceIDs, *approvalSource)
	if err != nil {
		return err
	}
	approval, err := uc.ApprovalRepository.GetApprovalByID(ctx, uc.DB.DB(), models.ApprovalRequestCondition{
		Where: models.ApprovalRequestWhere{
			ClientID:   claim.GetLoggedInClientID(),
			ApprovalID: id,
		},
	})

	if err != nil {
		return err
	}

	go uc.notifyStatusApproveRejectApproval(contexthelpers.WithoutCancel(ctx), approval)
	return err
}

// func (uc *ApprovalUseCase) getApprovalRequestBySourceCode(permissionRight *authhelpers.PermissionRight) (interface{}, error) {
// 	if len(permissionRight.PermissionCodes) == 0 || permissionRight == nil {
// 		return []string{}, errorhandler.ErrBadRequest("you not have permission")
// 	}
// 	return permissionRight.PermissionCodes, nil
// }

func (uc *ApprovalUseCase) GetApprovalRequest(ctx context.Context, req commonmodel.ListRequest) (commonmodel.ListResponse, error) {
	resp := commonmodel.ListResponse{
		PageSize: req.PageSize,
		PageNo:   req.PageNo,
	}
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return resp, err
	}
	permissionGroup, err := claim.GetPermissionCategory(constants.APPROVAL_PERMISSION_CODE)
	if err != nil {
		return resp, err
	}
	permissionCodes, err := uc.validatePermission(permissionGroup)
	if err != nil {
		return resp, err
	}
	count, approvalRequests, err := uc.ApprovalRepository.GetApprovalRequestsView(
		ctx,
		uc.DB.DB(),
		models.GetApprovalRequestListParam{
			ListRequest: req,
			Cond: models.ApprovalRequestCondition{
				Where: models.ApprovalRequestWhere{
					ClientID:        claim.GetLoggedInClientID(),
					PermissionCodes: permissionCodes,
				},
			},
		})
	if err != nil {
		return resp, err
	}
	if len(approvalRequests) == 0 {
		return resp, nil
	}

	var userIds []string
	var bonusPenaltyIds []string
	var assetStatusRequestIds []string
	for _, approvalRequest := range approvalRequests {
		userIds = append(userIds, approvalRequest.CreatedBy)
		for _, approval := range approvalRequest.Approvals {
			if approval.ApprovalUserID != "" {
				userIds = append(userIds, approval.ApprovalUserID)
			}
		}
		if approvalRequest.SourceCode == constants.APPROVAL_SOURCE_BONUS_PENALTY_TYRE {
			bonusPenaltyIds = append(bonusPenaltyIds, approvalRequest.ReferenceIDs[0])
			continue
		}
		assetStatusRequestIds = append(assetStatusRequestIds, approvalRequest.ReferenceIDs[0])
	}
	usersMapById := map[string]userIdentityModel.User{}
	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return resp, err
	}
	assetStatusRequests, err := uc.AssetStatusRequestRepository.GetAssetStatusRequestsByIDs(ctx, uc.DB.DB(), assetStatusRequestIds)
	if err != nil {
		return resp, err
	}
	bonusPenalties, err := uc.BonusPenaltyRepository.GetBonusPenaltiesByIDs(ctx, uc.DB.DB(), bonusPenaltyIds)
	if err != nil {
		return resp, err
	}
	var assetIds []string
	mapsReferenceIDAsset := map[string]string{}
	for _, assetStatusRequest := range assetStatusRequests {
		assetIds = append(assetIds, assetStatusRequest.AssetID)
		mapsReferenceIDAsset[assetStatusRequest.ID] = assetStatusRequest.AssetID
	}
	assets, err := uc.AssetRepository.GetAssetsByIDs(ctx, uc.DB.DB(), assetIds)
	if err != nil {
		return resp, err
	}
	mapsAsset := map[string]assetModel.Asset{}
	for _, val := range assets {
		mapsAsset[val.ID] = val
	}
	mapsBonusPenalties := map[string]assetModel.BonusPenalty{}
	for _, val := range bonusPenalties {
		mapsBonusPenalties[val.ID] = val
	}

	response := []dtos.ApprovalRequestResponse{}
	for _, val := range approvalRequests {
		dto := dtos.ApprovalRequestResponse{}
		dto.Set(val, usersMapById, mapsAsset[mapsReferenceIDAsset[val.ReferenceIDs[0]]], mapsBonusPenalties[val.ReferenceIDs[0]])
		response = append(response, dto)
	}

	resp.Data = response
	resp.TotalRecords = count

	return resp, nil
}

func (uc *ApprovalUseCase) validatePermission(permissionRight *authhelpers.PermissionRight) ([]string, error) {
	if len(permissionRight.PermissionCodes) == 0 || permissionRight == nil {
		return []string{}, errorhandler.ErrBadRequest("you not have permission")
	}
	return permissionRight.PermissionCodes, nil
}

func (uc *ApprovalUseCase) GetApprovalByID(ctx context.Context, id string) (*dtos.ApprovalResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	permissionGroup, err := claim.GetPermissionCategory(constants.APPROVAL_PERMISSION_CODE)
	if err != nil {
		return nil, err
	}
	permissionCodes, err := uc.validatePermission(permissionGroup)
	if err != nil {
		return nil, err
	}
	approval, err := uc.ApprovalRepository.GetApprovalByID(ctx, uc.DB.DB(), models.ApprovalRequestCondition{
		Where: models.ApprovalRequestWhere{
			ClientID:        claim.GetLoggedInClientID(),
			ApprovalID:      id,
			PermissionCodes: permissionCodes,
		},
	})

	if err != nil {
		return nil, err
	}

	if approval == nil {
		return nil, errorhandler.ErrDataNotFound("approval")
	}

	var userIds []string
	if approval.ApprovalUserID != "" {
		userIds = append(userIds, approval.ApprovalUserID)
	}
	userIds = append(userIds, approval.ApprovalRequest.CreatedBy)

	usersMapById := map[string]userIdentityModel.User{}
	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return nil, err
	}
	assets := []assetModel.Asset{}
	bonusPenalty := &assetModel.BonusPenalty{}
	// memberships := []inventoryModel.Membership{}
	membershipAssets := []assetModel.Asset{}
	refIDs := []string{}
	for _, refID := range approval.ApprovalRequest.ReferenceIDs {
		refIDs = append(refIDs, refID)
	}
	switch approval.ApprovalRequest.SourceCode {
	case constants.APPROVAL_SOURCE_DISPOSE_TYRE, constants.APPROVAL_SOURCE_SCRAP_TYRE:
		assetStatusRequests, err := uc.AssetStatusRequestRepository.GetAssetStatusRequests(ctx, uc.DB.DB(), assetModel.AssetStatusRequestCondition{
			Where: assetModel.AssetStatusRequestWhere{
				ClientID: claim.GetLoggedInClientID(),
				IDs:      approval.ApprovalRequest.ReferenceIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		assetIDs := []string{}
		for i := range assetStatusRequests {
			assetIDs = append(assetIDs, assetStatusRequests[i].AssetID)
		}
		assets, err = uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				IDs: assetIDs,
			},
		})
		if err != nil {
			return nil, err
		}
	case constants.APPROVAL_SOURCE_BONUS_PENALTY_TYRE:
		bonusPenalty, err = uc.BonusPenaltyRepository.GetBonusPenalty(ctx, uc.DB.DB(), assetModel.BonusPenaltyCondition{
			Where: assetModel.BonusPenaltyWhere{
				ClientID: claim.GetLoggedInClientID(),
				ID:       approval.ApprovalRequest.ReferenceIDs[0],
			},
		})
		if err != nil {
			return nil, err
		}
	case constants.APPROVAL_SOURCE_ASSET_MEMBERSHIP:
		memberships, err := uc.packageRepository.GetMembershipsByIDs(ctx, uc.DB.DB(), inventoryModel.MembershipCondition{
			Where: inventoryModel.MembershipWhere{
				ClientID: claim.GetLoggedInClientID(),
				IDs:      refIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		assetIds := make([]string, 0, len(memberships))
		for _, membership := range memberships {
			assetIds = append(assetIds, membership.AssetID)
		}
		membershipAssets, err = uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				ClientID: claim.GetLoggedInClientID(),
				IDs:      assetIds,
			},
		})
		if err != nil {
			return nil, err
		}
	}

	assetsResp := []dtos.AssetDTO{}
	for i := range assets {
		assetsResp = append(assetsResp, dtos.AssetDTO{
			ID:           assets[i].ID,
			Name:         assets[i].Name,
			SerialNumber: assets[i].SerialNumber,
			CategoryCode: assets[i].AssetCategoryCode,
		})
	}
	assetMembershipsResp := []dtos.AssetMembershipDTO{}
	for i := range membershipAssets {
		assetMembershipsResp = append(assetMembershipsResp, dtos.AssetMembershipDTO{
			AssetID:         membershipAssets[i].ID,
			ReferenceNumber: membershipAssets[i].ReferenceNumber,
			SerialNumber:    membershipAssets[i].SerialNumber,
		})
	}

	resp := &dtos.ApprovalResponse{
		ID:                 approval.ID,
		CreatedBy:          approval.CreatedBy,
		CreatedAt:          approval.CreatedAt,
		UpdatedAt:          approval.UpdatedAt,
		ApprovalRequestID:  approval.ApprovalRequestID,
		UserPermissionCode: approval.UserPermissionCode,
		ApprovalNotes:      approval.ApprovalNotes,
		ApprovalUserID:     approval.ApprovalUserID,
		StatusCode:         approval.StatusCode,
		ApprovalRequest: dtos.ApprovalRequestDTO{
			ID:                    approval.ApprovalRequest.ID,
			CreatedBy:             approval.ApprovalRequest.CreatedBy,
			CreatedAt:             approval.ApprovalRequest.CreatedAt,
			ApprovalNumber:        approval.ApprovalRequestView.ApprovalNumber,
			SourceCode:            approval.ApprovalRequest.SourceCode,
			ReferenceIDs:          approval.ApprovalRequest.ReferenceIDs,
			Description:           approval.ApprovalRequest.Description,
			RequestNotes:          approval.ApprovalRequest.RequestNotes,
			StatusCode:            approval.ApprovalRequest.StatusCode,
			RequesterUserFullName: usersMapById[approval.ApprovalRequest.CreatedBy].GetName(),
			Assets:                assetsResp,
			AssetMemberships:      assetMembershipsResp,
			BonusPenalty: dtos.BonusPenaltyDTO{
				ID:        bonusPenalty.ID,
				BAPNumber: bonusPenalty.BAPNumber,
			},
		},
		ApprovalUserFullName: usersMapById[approval.ApprovalUserID].GetName(),
	}

	return resp, nil
}

func (uc *ApprovalUseCase) constructApproval(ctx context.Context, approvalRequest *models.ApprovalRequest) error {
	approvalSource, err := uc.ApprovalRepository.GetApprovalSource(ctx, uc.DB.DB(), approvalRequest.SourceCode)
	if err != nil {
		return err
	}
	for _, val := range approvalSource.UserPermissionCodes {
		approval := models.Approval{}
		switch val {
		case constants.APPROVAL_SOURCE_SCRAP_TYRE:
			approval.UserPermissionCode = constants.APPROVAL_SOURCE_SCRAP_TYRE
		case constants.APPROVAL_SOURCE_DISPOSE_TYRE:
			approval.UserPermissionCode = constants.APPROVAL_SOURCE_DISPOSE_TYRE
		case constants.APPROVAL_SOURCE_BONUS_PENALTY_TYRE:
			approval.UserPermissionCode = constants.APPROVAL_SOURCE_BONUS_PENALTY_TYRE
		case constants.APPROVAL_SOURCE_ASSET_MEMBERSHIP:
			approval.UserPermissionCode = constants.APPROVAL_SOURCE_ASSET_MEMBERSHIP
		case constants.APPROVAL_SOURCE_CREATE_NEW_ASSET:
			approval.UserPermissionCode = constants.APPROVAL_SOURCE_CREATE_NEW_ASSET
		default:
			return errorhandler.ErrBadRequest("unknown source codes")
		}
		approval.StatusCode = constants.APPROVAL_STATUS_PENDING
		approvalRequest.Approvals = append(approvalRequest.Approvals, &approval)
	}
	return nil
}

func (uc *ApprovalUseCase) CreateApprovalRequest(ctx context.Context, dB database.DBI, approvalRequest *models.ApprovalRequest) error {
	err := uc.constructApproval(ctx, approvalRequest)
	if err != nil {
		return err
	}
	approvalRequest.StatusCode = constants.APPROVAL_STATUS_PENDING
	err = uc.ApprovalRepository.CreateApprovalRequest(ctx, dB, approvalRequest)
	if err != nil {
		return err
	}
	return nil
}
