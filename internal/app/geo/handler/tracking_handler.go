package handler

import (
	"assetfindr/internal/app/geo/dtos"
	"assetfindr/internal/app/geo/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"bytes"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
)

type TrackingHandler struct {
	trackingUsecase *usecase.TrackingUseCase
}

func NewTrackingHandler(
	trackingUsecase *usecase.TrackingUseCase,
) *TrackingHandler {
	return &TrackingHandler{
		trackingUsecase: trackingUsecase,
	}
}

func (h *TrackingHandler) GetTrackings(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	req := dtos.TrackingListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.trackingUsecase.GetTrackings(ctx, req, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestVehicleKM(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	resp, err := h.trackingUsecase.GetLatestVehicleKMFromCanBus(ctx, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestTrackingPosition(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	resp, err := h.trackingUsecase.GetLatestTrackingPosition(ctx, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestTrackingPositionList(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	req := dtos.LatestListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.trackingUsecase.GetLatestTrackingPositionList(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestCanBusData(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	resp, err := h.trackingUsecase.GetLatestCanBusData(ctx, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestGeneralData(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	resp, err := h.trackingUsecase.GetLatestGeneralData(ctx, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestCompressorData(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	resp, err := h.trackingUsecase.GetLatestCompressorData(ctx, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestTyreSensorData(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	var req dtos.GetLatestTrackingReq
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.trackingUsecase.GetLatestTyreSensorData(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestTyreChangerData(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	var req dtos.GetLatestTrackingReq
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.trackingUsecase.GetLatestTyreChangerData(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestTyreChangerStateData(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	var req dtos.GetLatestTrackingReq
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.trackingUsecase.GetLatestTyreChangerStateData(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestHMDigitalInputData(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	var req dtos.GetLatestTrackingReqV2
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.trackingUsecase.GetLatestHMDigitalInputData(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestCanBusDataList(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	req := dtos.LatestListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	resp, err := h.trackingUsecase.GetLatestCanBusDataList(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestGeneralDataList(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	req := dtos.LatestListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	resp, err := h.trackingUsecase.GetLatestGeneralDataList(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestCompressorDataList(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	req := dtos.LatestListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	resp, err := h.trackingUsecase.GetLatestCompressorDataList(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestTyreSensorDataList(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	req := dtos.LatestListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	resp, err := h.trackingUsecase.GetLatestTyreSensorDataList(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestTyreChangerDataList(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	req := dtos.LatestListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	resp, err := h.trackingUsecase.GetLatestTyreChangerDataList(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetLatestTyreChangerStateDataList(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	req := dtos.LatestListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	resp, err := h.trackingUsecase.GetLatestTyreChangerStateDataList(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) CreateGeoFence(c *gin.Context) {
	ctx := c.Request.Context()

	var req dtos.CreateGeoFence
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = req.Coordinates.Validate()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.trackingUsecase.CreateGeoFence(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// delete
func (h *TrackingHandler) DeleteGeoFence(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.trackingUsecase.DeleteGeoFence(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetGeoFence(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.trackingUsecase.GetGeoFence(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) GetGeoFences(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GeoFenceListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.trackingUsecase.GetGeoFences(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) CheckGeoFences(c *gin.Context) {
	ctx := c.Request.Context()

	var req struct {
		IntervalMinutes int `json:"interval_minutes"`
	}
	_ = c.BindJSON(&req)

	err := h.trackingUsecase.CheckGeoFences(ctx, req.IntervalMinutes)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	})
}

func (h *TrackingHandler) GetGeoFenceInOutHistories(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GeofenceInOutHistoryListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.trackingUsecase.GetGeoFenceInOutHistories(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) CreateSendquipDataV1(c *gin.Context) {
	ctx := c.Request.Context()
	buf := new(bytes.Buffer)
	buf.ReadFrom(c.Request.Body)
	str := buf.String()

	commonlogger.Infof("log-testting %s", str)

	var req dtos.SendquipCompressorReq
	err := json.Unmarshal([]byte(str), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.trackingUsecase.CreateLogRawSensorDatalakeSendquip(req, str)

	resp, err := h.trackingUsecase.SaveSenquipDataV1(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TrackingHandler) CreateSendquipDataV2(c *gin.Context) {
	ctx := c.Request.Context()
	buf := new(bytes.Buffer)
	buf.ReadFrom(c.Request.Body)
	req := buf.Bytes()

	commonlogger.Infof("log-testting %s", req)

	err := h.trackingUsecase.ProcessIntegrationsDataMapping(ctx, req, false)
	if err != nil && !errorhandler.IsErrNotFound(err) {
		commonlogger.Errorf("falied to process integration data mapping data err: %v", err)
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	})
}

func (h *TrackingHandler) CreateLogSensorData(c *gin.Context) {
	buf := new(bytes.Buffer)
	buf.ReadFrom(c.Request.Body)
	str := buf.String()

	commonlogger.Infof("log-testting %s", str)

	h.trackingUsecase.CreateLogRawSensorDatalake(str)

	c.JSON(http.StatusOK, commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	})
}

func (h *TrackingHandler) TestSendquip(c *gin.Context) {
	buf := new(bytes.Buffer)
	buf.ReadFrom(c.Request.Body)
	str := buf.String()

	commonlogger.Infof("log-testting %s", str)

	var req dtos.SendquipCompressorReq
	err := json.Unmarshal([]byte(str), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.trackingUsecase.CreateLogRawSensorDatalakeSendquip(req, str)

	c.JSON(http.StatusOK, map[string]interface{}{})
}

// func (h *TrackingHandler) CreateSendquipData(c *gin.Context) {
// 	ctx := c.Request.Context()
// 	buf := new(bytes.Buffer)
// 	buf.ReadFrom(c.Request.Body)
// 	str := buf.String()

// 	commonlogger.Infof("log-testting %s", str)

// 	var req dtos.SendquipReq
// 	err := json.Unmarshal([]byte(str), &req)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
// 		return
// 	}

// 	resp, err := h.trackingUsecase.SaveSenquipData(ctx, req)
// 	if err != nil {
// 		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
// 		c.JSON(httpStatus, gin.H{"error": errMessage})
// 		return
// 	}

// 	c.JSON(http.StatusOK, resp)
// }
