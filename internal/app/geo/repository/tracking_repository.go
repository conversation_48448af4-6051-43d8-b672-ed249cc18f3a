package repository

import (
	"assetfindr/cmd/flespimqttservice/model"
	"assetfindr/internal/app/geo/models"
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type TrackingRepository interface {
	GetTrackings(ctx context.Context, dB database.DBI, cond models.TrackingCondition) ([]models.Tracking, error)
	GetLatestVehicleKM(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.Tracking, error)
	GetLatestVehicleKMFromCanBus(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.CanBusSensorData, error)
	GetLatestTrackingPosition(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.Tracking, error)
	CreateTrackings(ctx context.Context, dB database.DBI, trackings []models.Tracking) error
	CreatePreGPSVehicleMeter(ctx context.Context, dB database.DBI, preGPSVehicleMeter *models.PreGPSVehicleMeter) error
	GetPreGPSVehicleMeter(ctx context.Context, dB database.DBI, cond models.PreGPSVehicleMeterCondition) (*models.PreGPSVehicleMeter, error)
	UpdatePreGPSVehicleMeter(ctx context.Context, dB database.DBI, id string, data *models.PreGPSVehicleMeter) error
	CreateGeoFence(ctx context.Context, dB database.DBI, geoFence *models.GeoFence) error
	UpdateGeoFence(ctx context.Context, dB database.DBI, internalReferenceID string, geoFence *models.GeoFence) error
	GetGeoFenceByAssetID(ctx context.Context, dB database.DBI, assetID string, clientID string) (*models.GeoFence, error)
	GetGeoFencesByAssetID(ctx context.Context, dB database.DBI, assetID string) ([]models.GeoFence, error)
	GetAllGeoFences(ctx context.Context, dB database.DBI) ([]models.GeoFence, error)
	GetGeoFenceByID(ctx context.Context, dB database.DBI, id string, clientID string) (*models.GeoFence, error)
	GetGeoFenceByAssetIDs(ctx context.Context, dB database.DBI, assetIDs []string, id string, clientID string) (*models.GeoFence, error)
	DeleteGeoFenceReferences(ctx context.Context, dB database.DBI, ids []string) error
	DeleteGeoFenceReferenceByGeoFenceID(ctx context.Context, dB database.DBI, id string) error
	DeleteGeoFence(ctx context.Context, dB database.DBI, id string) error
	UpdateGeoFenceReference(ctx context.Context, dB database.DBI, id string, geoFenceReference *models.GeoFenceReference) error
	CreateGeoFenceReferences(ctx context.Context, dB database.DBI, geoFenceReferences []models.GeoFenceReference) error
	GetGeoFence(ctx context.Context, dB database.DBI, cond models.GeoFenceCondition) (*models.GeoFence, error)
	GetGeoFences(ctx context.Context, dB database.DBI, cond models.GeoFenceCondition) ([]models.GeoFence, error)
	GetGeoFenceList(ctx context.Context, dB database.DBI, param models.GetGeoFenceListParam) (int, []models.GeoFence, error)
	GetGeoFenceReferences(ctx context.Context, dB database.DBI, cond models.GeoFenceReferenceCondition) ([]models.GeoFenceReference, error)
	GetGeoFenceReference(ctx context.Context, dB database.DBI, cond models.GeoFenceReferenceCondition) (*models.GeoFenceReference, error)

	GetGeofenceInOutHistoryList(ctx context.Context, dB database.DBI, param models.GetGeofenceInOutHistoryListParam) (string, []models.GeofenceInOutHistory, error)
	CreateGeofenceInOutHistory(ctx context.Context, dB database.DBI, GeofenceInOutHistory *models.GeofenceInOutHistory) error
	GetGeofenceInOutHistory(ctx context.Context, dB database.DBI, condition models.GeofenceInOutHistoryCondition) (*models.GeofenceInOutHistory, error)
	GetGeofenceInOutHistorys(ctx context.Context, dB database.DBI, condition models.GeofenceInOutHistoryCondition) ([]models.GeofenceInOutHistory, error)

	CreateGeoRouteFence(ctx context.Context, dB database.DBI, geoRouteFence *models.GeoRouteFence) error
	GetGeoRouteFence(ctx context.Context, dB database.DBI) (*models.GeoRouteFence, error)

	CreateCanBusSensor(ctx context.Context, dB database.DBI, canBusData *models.CanBusSensorData) error
	GetLatestCanBusData(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.CanBusSensorData, error)

	CreateCompressorSensor(ctx context.Context, dB database.DBI, compressorData *models.CompressorSensorData) error
	GetLatestCompressorData(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.CompressorSensorData, error)
	CreateCompressorSensorDataLake(ctx context.Context, bQ bq.BQI, compressorData *models.CompressorSensorData) error

	CreateGeneralSensor(ctx context.Context, dB database.DBI, generalSensorData *models.GeneralSensor) error
	CreateGeneralSensorDataLake(ctx context.Context, bQ bq.BQI, generalSensorData *models.GeneralSensor) error
	GetLatestGeneralData(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.GeneralSensor, error)
	GetLatestGeneralSensorDataLakeByDistinctIDs(ctx context.Context, bQ bq.BQI) ([]models.GeneralSensorBQ, error)

	CreateLogRawSensorDataLake(ctx context.Context, bQ bq.BQI, logRawSensorData *models.LogRawSensorData) error
	CreateLogRawSensorDatasLake(ctx context.Context, bQ bq.BQI, logRawSensorDatas []*models.LogRawSensorData) error

	GetLatestTrackingPositionList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.Tracking, error)
	GetLatestCanBusDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.CanBusSensorData, error)
	GetLatestCompressorDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.CompressorSensorData, error)
	GetLatestGeneralDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.GeneralSensor, error)

	CreateGpsSensorDataLake(ctx context.Context, bQ bq.BQI, gpsSensorData *models.GpsSensor) error
	CreateCanBusSensorDataLake(ctx context.Context, bQ bq.BQI, canBusSensorData *models.CanBusSensorData) error
	CreateFlespiSensorDataLake(ctx context.Context, bQ bq.BQI, flespiSensorData *model.FlespiData) error

	CreateTyreSensor(ctx context.Context, dB database.DBI, tyreSensorData *models.TyreSensor) error
	CreateTyreSensorDataLake(ctx context.Context, bQ bq.BQI, tyreSensorData *models.TyreSensor) error
	GetLatestTyreSensorData(ctx context.Context, dB database.DBI, req models.GetLatestTrackingParam) (*models.TyreSensor, error)
	GetLatestTyreSensorDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.TyreSensor, error)
	GetLatestTyreSensorDataLakeByDistinctIDs(ctx context.Context, bQ bq.BQI) ([]models.TyreSensorBQ, error)

	IncreaseTyreChangerHM(ctx context.Context, dB database.DBI, tyreChangerHM *models.TyreChangerHM) (int, error)
	CreateTyreChanger(ctx context.Context, dB database.DBI, tyreSensorData *models.TyreChanger) error
	CreateTyreChangerDataLake(ctx context.Context, bQ bq.BQI, tyreSensorData *models.TyreChanger) error
	GetLastetTyreChangerHM(ctx context.Context, dB database.DBI, integrationID string) (int, error)

	CreateTyreChangerState(ctx context.Context, dB database.DBI, tyreChangerStateData *models.TyreChangerState) error
	CreateTyreChangerStateDataLake(ctx context.Context, bQ bq.BQI, tyreChangerStateData *models.TyreChangerState) error

	GetLatestTyreChangerData(ctx context.Context, dB database.DBI, req models.GetLatestTrackingParam) (*models.TyreChanger, error)
	GetLatestTyreChangerDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.TyreChanger, error)
	GetLatestTyreChangerStateData(ctx context.Context, dB database.DBI, req models.GetLatestTrackingParam) (*models.TyreChangerState, error)
	GetLatestTyreChangerStateDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.TyreChangerState, error)

	GetLatestHMDigitalInputData(ctx context.Context, dB database.DBI, req models.GetLatestTrackingParam) (*models.GeneralSensorDigitalInputHM, error)

	UpsertLatestIntegrationData(ctx context.Context, dB database.DBI, latestIntegrationData *models.LatestIntegrationData) error
	GetLatestIntegrationDataList(ctx context.Context, dB database.DBI) ([]models.LatestIntegrationData, error)
}
