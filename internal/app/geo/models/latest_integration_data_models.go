package models

import (
	"time"

	"github.com/jackc/pgtype"
)

type LatestIntegrationData struct {
	IntegrationID string       `json:"integration_id" gorm:"column:integration_id"`
	DataJSON      pgtype.JSONB `json:"data_json" gorm:"column:data_json"`
	ClientID      string       `json:"client_id" gorm:"column:client_id"`
	UpdatedAt     time.Time    `json:"updated_at" gorm:"column:updated_at"`
}

func (LatestIntegrationData) TableName() string {
	return "ssr_latest_integration_data"
}
