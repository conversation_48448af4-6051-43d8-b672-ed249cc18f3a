package models

import (
	"encoding/json"
	"time"

	"cloud.google.com/go/bigquery"
	"gopkg.in/guregu/null.v4"
)

type TyreSensor struct {
	AssetID         string      `json:"asset_id" gorm:"column:asset_id"`
	Time            time.Time   `json:"time" gorm:"column:time"`
	Ident           string      `json:"ident" gorm:"column:ident"`
	CreatedAt       time.Time   `json:"created_at" gorm:"column:created_at"`
	IntegrationID   string      `json:"integration_id" gorm:"column:integration_id"`
	ClientID        string      `json:"client_id" gorm:"column:client_id"`
	IdentMacAddress null.String `json:"ident_mac_address" gorm:"column:ident_mac_address"`
	BatteryVoltage  null.Float  `json:"battery_voltage" gorm:"column:battery_voltage"`
	Pressure        null.Float  `json:"pressure" gorm:"column:pressure"`
	Temperature     null.Float  `json:"temperature" gorm:"column:temperature"`
	TyrePosition    null.Int    `json:"tyre_position" gorm:"column:tyre_position"`
	ParentAssetID   null.String `json:"parent_asset_id" gorm:"column:parent_asset_id"`
	TyreRowPosition null.Int    `json:"tyre_row_position" gorm:"column:tyre_row_position"`
}

func (i *TyreSensor) Save() (map[string]bigquery.Value, string, error) {
	var myMap map[string]bigquery.Value
	data, _ := json.Marshal(*i)
	json.Unmarshal(data, &myMap)
	return myMap, bigquery.NoDedupeID, nil
}

func (TyreSensor) TableName() string {
	return "ssr_tyre_sensor_data"
}

type TyreSensorBQ struct {
	AssetID       string    `bigquery:"asset_id"`
	Time          time.Time `bigquery:"time"`
	CreatedAt     time.Time `bigquery:"created_at"`
	IntegrationID string    `bigquery:"integration_id"`
}
