package usecase

import (
	assetConstant "assetfindr/internal/app/asset/constants"
	inspectionModels "assetfindr/internal/app/asset/models"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"

	orderModel "assetfindr/internal/app/inventory/models"
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/dtos"
	"assetfindr/internal/app/task/models"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"fmt"
	"html/template"
)

func (uc *TicketUseCase) CreateTicketNote(ctx context.Context, ticketId string, req dtos.TicketNoteReq) (*models.TicketNote, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	ticket, err := uc.TicketRepository.GetTicketByID(ctx, uc.DB.DB(), ticketId)
	if err != nil {
		return nil, err
	}

	if ticket.ClientID != claim.GetLoggedInClientID() {
		return nil, errorhandler.ErrNotEligible
	}
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer func() {
		tx.Rollback()
	}()
	hasAttachment := false
	if len(req.Attachments) > 0 {
		hasAttachment = true
	}
	note := models.TicketNote{
		TicketID:      ticketId,
		UserID:        claim.UserID,
		Title:         req.Title,
		Notes:         req.Notes,
		HasAttachment: hasAttachment,
		ClientID:      claim.GetLoggedInClientID(),
	}
	err = uc.TicketRepository.CreateNote(ctx, tx.DB(), &note)
	if err != nil {
		return nil, err
	}
	_, err = uc.attachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_TICKET_NOTE,
		SourceReferenceID: note.ID,
		TargetReferenceID: ticket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Attachments,
	})
	if err != nil {
		return nil, err
	}
	err = tx.Commit()
	if err != nil {
		commonlogger.Errorf("Error create ticket note when commit to database: %v\n", err)
		return nil, err
	}
	go uc.notifyAfterAddTicketNote(contexthelpers.WithoutCancel(ctx), ticket, req)
	return &note, nil
}

func (uc *TicketUseCase) notifyAfterAddTicketNote(
	ctx context.Context,
	ticket *models.Ticket,
	req dtos.TicketNoteReq,
) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return
	}

	assetName := ""
	platNo := ""
	asset := &inspectionModels.Asset{}
	if ticket.ReferenceID != constants.GENERAL_TICKET_REFERENCE_ID {
		if ticket.TicketReferenceCode == constants.TICKET_ASSET_VEHICLE_REF {
			asset, err = uc.AssetUseCase.GetAssetByID(ctx, ticket.ReferenceID)
			if err != nil {
				commonlogger.Warnf("error get asset on notify update ticket notes", err)
				return
			}

			if asset.ReferenceNumber == "" {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.SerialNumber)
				platNo = asset.SerialNumber
			} else {
				assetName = fmt.Sprintf("%s - %s", asset.Name, asset.ReferenceNumber)
				platNo = asset.ReferenceNumber
			}
		}
	}

	if ticket.AssignedToUserID.String == "" {
		return
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), userIdentityModel.UserCondition{
		Where: userIdentityModel.UserWhere{
			ID: ticket.AssignedToUserID.String,
		},
	})
	if err != nil {
		commonlogger.Warnf("error get user on notify update ticket status", err)
		return
	}

	client, err := uc.UserRepository.GetClient(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
		Where: userIdentityModel.ClientWhere{
			ID: ticket.ClientID,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get client for email %v", err)
		return
	}

	statusLabel := constants.MapStatusCodeToLabel()[ticket.StatusCode]
	href := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notifConstants.DESTINATION_TYPE_WORK_ORDER, ticket.ID)

	inspectionAssignees := make([]inspectionModels.AssetInspectionAssignment, 0)
	isFromWorkshop := ticket.TicketCategoryCode == constants.TICKET_CATEGORY_CODE_WORKSHOP_SERVICE
	orderItems := make([]orderModel.OrderItem, 0)
	if isFromWorkshop {
		inspection, _ := uc.assetInspectionRepo.GetAssetInspection(ctx, uc.DB.DB(), inspectionModels.AssetInspectionCondition{
			Where: inspectionModels.AssetInspectionWhere{
				ReferenceID:   ticket.ID,
				ReferenceCode: assetConstant.ASSET_INSPECTION_REFERENCE_CODE_WORK_ORDER,
			},
		})

		if inspection != nil {
			inspectionAssignees, err = uc.assetInspectionRepo.GetAssetInspectionAssignments(ctx, uc.DB.DB(), inspectionModels.AssetInspectionAssignmentCondition{
				Where: inspectionModels.AssetInspectionAssignmentWhere{
					InspectionID: inspection.ID,
				},
			})
			if err != nil {
				commonlogger.Warnf("failed to get inspection assignees on work order add note %v", err)
				return
			}
		}

		order, err := uc.OrderRepo.GetOrder(ctx, uc.DB.DB(), orderModel.OrderCondition{
			Where: orderModel.OrderWhere{
				SourceReferenceID: ticket.ID,
			},
		})
		if err != nil {
			commonlogger.Warnf("failed to get order on work order add note %v", err)
			return
		}

		_, items, err := uc.OrderRepo.GetOrderItemList(ctx, uc.DB.DB(), orderModel.GetOrderItemListParam{
			Cond: orderModel.OrderItemCondition{
				Where: orderModel.OrderItemWhere{
					OrderId: order.ID,
				},
			},
		})
		if err != nil {
			commonlogger.Warnf("failed to get order items on work order add note %v", err)
			return
		}
		orderItems = items
	}

	templateBod := tmplhelpers.TicketNoteBody{
		Subject:        ticket.Subject,
		AssetName:      assetName,
		Adder:          user.GetName(),
		Status:         statusLabel,
		RedirectWOLink: template.URL(href),
		Note:           req.Notes,
		IsFromWorkshop: isFromWorkshop,
		PlatNo:         "-",
		CustomerName:   "Customer Name",
	}
	if asset.PartnerOwnerName != "" {
		templateBod.CustomerName = asset.PartnerOwnerName
	}
	if platNo != "" {
		templateBod.PlatNo = platNo
	}

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            ticket.AssignedToUserID.String,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TICKET_UPDATE,
		SourceReferenceID: ticket.ID,
		TargetReferenceID: ticket.ReferenceID,
		TargetURL:         href,
		MessageHeader:     templateBod.ConstructSubjectEmail(),
		MessageBody:       templateBod.ConstructBodyEmail(),
		MessageFirebase: notificationDtos.MessageFirebase{
			Title: templateBod.ConstructSubjectPushNotif(),
			Body:  templateBod.ConstructBodyPushNotif(),
		},
		ClientID:        ticket.ClientID,
		TypeCode:        "",
		ContentTypeCode: "",
		ReferenceCode:   notifConstants.NOTIF_REF_WORK_ORDER,
		ReferenceValue:  ticket.ID,
	}

	notifItems := make([]notificationDtos.CreateNotificationItem, 0)
	isRequester := ticket.RequesterUserID == claim.UserID
	inspectionAssign := make(map[string]bool, 0)
	spkAssign := make(map[string]bool, 0)

	if isRequester {
		notifItem.UserID = ticket.AssignedToUserID.String
		notifItem.UserID = ticket.AssignedToUserID.String
		notifItems = append(notifItems, notifItem)
	} else {
		// vehicle and tyre
		for i := range inspectionAssignees {
			if ticket.AssignedToUserID.String == inspectionAssignees[i].UserID {
				continue
			}
			inspectionAssign[inspectionAssignees[i].UserID] = true
			notifItem.UserID = inspectionAssignees[i].UserID
			notifItems = append(notifItems, notifItem)
		}
		// SPK
		for i := range orderItems {
			if ticket.AssignedToUserID.String == orderItems[i].AssigneeUserID {
				continue
			}
			spkAssign[orderItems[i].AssigneeUserID] = true
			notifItem.UserID = orderItems[i].AssigneeUserID
			notifItems = append(notifItems, notifItem)
		}
	}

	if inspectionAssign[claim.UserID] || spkAssign[claim.UserID] {
		notifItems = nil
		notifItem.UserID = ticket.AssignedToUserID.String
		notifItems = append(notifItems, notifItem)
	}

	// to remove duplicate recipient
	uniqueMap := make(map[notificationDtos.CreateNotificationItem]bool)
	var result []notificationDtos.CreateNotificationItem
	for _, value := range notifItems {
		if !uniqueMap[value] {
			uniqueMap[value] = true
			result = append(result, value)
		}
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           result,
		SendToEmail:     true,
		SendToPushNotif: true,
	})
}

func (uc *TicketUseCase) GetTicketNotes(ctx context.Context, ticketId string) ([]dtos.TicketGetNotesResponseDto, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	ticket, err := uc.TicketRepository.GetTicketByID(ctx, uc.DB.DB(), ticketId)
	if err != nil {
		return nil, err
	}

	if ticket.ClientID != claim.GetLoggedInClientID() {
		return []dtos.TicketGetNotesResponseDto{}, nil
	}

	notes, err := uc.TicketRepository.GetNotes(ctx, uc.DB.DB(), models.TicketNoteCondition{
		Where: models.TicketNoteWhere{
			TicketID: ticketId,
		},
	})
	if err != nil {
		return nil, err
	}

	// Get user by Ids
	var userIds []string

	for _, note := range notes {
		if note.UserID != "" {
			userIds = append(userIds, note.UserID)
		}
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return nil, err
	}

	newResponse := dtos.BuildTicketGetNotesResponse(notes, usersMapById)

	return newResponse, nil
}
