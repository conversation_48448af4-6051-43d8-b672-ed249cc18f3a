package routers

import (
	"assetfindr/internal/app/task/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterTicketRoutes(route *gin.Engine, ticketHandler *handler.TicketHandler) *gin.Engine {
	ticketRoutes := route.Group("/v1/tickets", middleware.TokenValidationMiddleware())
	{
		ticketRoutes.GET("", ticketHandler.GetTickets)
		ticketRoutes.GET("/list", ticketHandler.GetTicketList)
		ticketRoutes.GET("/categories", ticketHandler.GetTicketCategoryList)
		ticketRoutes.POST("/categories", ticketHandler.CreateTicketCategory)
		ticketRoutes.PUT("/categories/:id", ticketHandler.UpdateStatusTicketCategory)
		ticketRoutes.GET("/categories/:id", ticketHandler.GetTicketCategory)
		ticketRoutes.GET("/:id", ticketHandler.GetTicketByID)
		ticketRoutes.GET("/counts", ticketHandler.GetTicketCount)
		ticketRoutes.POST("", ticketHandler.CreateTicket)
		ticketRoutes.POST("/v2", ticketHandler.CreateTicketV2)
		ticketRoutes.PUT("/v2/:id", ticketHandler.UpdateTicketV2)
		ticketRoutes.PUT("/v2/:id/due-date", ticketHandler.UpdateTicketV2DueDate)
		ticketRoutes.PUT("/:id", ticketHandler.UpdateTicket)
		ticketRoutes.PUT("/:id/close", ticketHandler.CloseTicket)
		ticketRoutes.PUT("/:id/cancel", ticketHandler.CancelTicket)
		ticketRoutes.PUT("/:id/assign", ticketHandler.AssignTicketToUser)
		ticketRoutes.PUT("/:id/resolution", ticketHandler.AddTicketResolution)
		ticketRoutes.PUT("/:id/inprogress", ticketHandler.UpdateTicketStatusInProgress)
		ticketRoutes.PUT("/:id/onhold", ticketHandler.UpdateTicketStatusOnHold)
		ticketRoutes.PUT("/:id/severity-level", ticketHandler.UpdateSeverityLevelCode)
		ticketRoutes.PUT("/:id/schedule", ticketHandler.UpdateSchedule)
		ticketRoutes.PUT("/:id/unschedule", ticketHandler.UpdateSchedule)
		ticketRoutes.PUT("/:id/status", ticketHandler.UpdateTicketStatus)

		ticketRoutes.POST("/:id/notes", ticketHandler.CreateTicketNotes)
		ticketRoutes.GET("/:id/notes", ticketHandler.GetTicketNotes)
		ticketRoutes.GET("/:id/linked", ticketHandler.GetLinkedTicketList)
		ticketRoutes.POST("/linked", ticketHandler.CreateLinkTicket)
		ticketRoutes.POST("/unlinked", ticketHandler.UnlinkTicket)
		ticketRoutes.GET("/:id/contacts", ticketHandler.GetTicketContactsByTicketID)
	}

	adminTicketRoutes := route.Group("/v1/admin/tickets", middleware.APITokenMiddleware)
	{
		adminTicketRoutes.POST("/auto-create", ticketHandler.AutoCreateTickets)
		adminTicketRoutes.POST("/auto-close", ticketHandler.CloseTicketWithScheduler)
	}

	jobTicketRoutes := route.Group("/v1/jobs/tickets", middleware.APITokenMiddleware)
	{
		jobTicketRoutes.POST("/duedate-reminder", ticketHandler.JobRemindTicketDueDate)
		jobTicketRoutes.POST("/schedule-reminder", ticketHandler.JobRemindTicketSchedule)
	}

	jobTaskRoutes := route.Group("/v1/jobs/tasks", middleware.APITokenMiddleware)
	{
		jobTaskRoutes.POST("/schedule-reminder", ticketHandler.JobRemindTaskSchedule)
	}

	ticketChartRoute := route.Group("/v1/tickets/charts", middleware.TokenValidationMiddleware())
	{
		ticketChartRoute.GET("/ticket-status", ticketHandler.ChartTicketStatus)
		ticketChartRoute.GET("/total-ticket", ticketHandler.ChartTotalTickets)
		ticketChartRoute.GET("/ticket-overdue", ticketHandler.ChartTicketOverdue)
	}

	ticketContactRoutes := route.Group("/v1/tickets/contacts", middleware.TokenValidationMiddleware())
	{
		ticketContactRoutes.POST("", ticketHandler.UpsertTicketContact)
	}

	taskRoutes := route.Group("/v1/tasks", middleware.TokenValidationMiddleware())
	{
		taskRoutes.POST("", ticketHandler.CreateTask)
		taskRoutes.PUT("/:id", ticketHandler.UpdateTask)
		taskRoutes.DELETE("/:id", ticketHandler.DeleteTask)
		taskRoutes.GET("/:id", ticketHandler.GetTask)
		taskRoutes.GET("", ticketHandler.GetTasks)
		taskRoutes.PUT("/:id/schedule", ticketHandler.ScheduleTask)
		taskRoutes.PUT("/:id/complete", ticketHandler.CompleteTask)
	}

	taskChartRoute := route.Group("/v1/tasks/charts", middleware.TokenValidationMiddleware())
	{
		taskChartRoute.GET("/count-open", ticketHandler.ChartCountOpenTasks)
		taskChartRoute.GET("/count-overdue", ticketHandler.ChartCountOverdueTaks)
	}

	return route
}
