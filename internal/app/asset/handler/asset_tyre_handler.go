package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gocarina/gocsv"
)

type AssetTyreHandler struct {
	AssetTyreUseCase  *usecase.AssetTyreUseCase
	AttachmentUseCase *storageUsecase.AttachmentUseCase
}

func NewAssetTyreHandler(recipeUseCase *usecase.AssetTyreUseCase, attachmentUseCase *storageUsecase.AttachmentUseCase) *AssetTyreHandler {
	return &AssetTyreHandler{
		AssetTyreUseCase:  recipeUseCase,
		AttachmentUseCase: attachmentUseCase,
	}
}

func (h *AssetTyreHandler) GetAssetTyres(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyresRequest{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	recipesResponse, err := h.AssetTyreUseCase.GetAssetTyreList(ctx, req)
	if err != nil {
		commonlogger.Errorf("Failed in getting asset tyres ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in getting asset tyres"})
		return
	}

	c.JSON(http.StatusOK, recipesResponse)
}

func (h *AssetTyreHandler) GetAssetTyreByID(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("id")

	resp, err := h.AssetTyreUseCase.GetAssetTyreByIDV2(ctx, assetID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}
	// response := dtos.ResponseAssetTyreById{
	// 	AssetTyre:    assetTyre,
	// 	TUR:          tUR,
	// 	TyrePosition: tyrePosition,
	// }
	c.JSON(http.StatusOK, resp)
}

func (ah *AssetTyreHandler) CreateAssetTyre(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.BulkAssetTyreReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = ah.AssetTyreUseCase.ValidateCreateAssetTyreSpecifications(ctx, req)
	if err != nil {
		_, message := errorhandler.ParseToHttpError(err)
		c.JSON(http.StatusBadRequest, gin.H{"error": message})
		return
	}

	resp, err := ah.AssetTyreUseCase.CreateAssetTyreV2(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetTyreHandler) GetAssetTyresByIds(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.AssetTyreByIDsListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		commonlogger.Warnf("Failed when get list linked tyre history request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	ids := c.QueryArray("ids")

	response, err := h.AssetTyreUseCase.GetAssetTyresByIds(ctx, ids, req)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *AssetTyreHandler) GetAssetTyresByLinkedParentAssetId(c *gin.Context) {
	ctx := c.Request.Context()
	parentAssetId := c.Query("parent_asset_id")

	response, err := h.AssetTyreUseCase.GetAssetTyresByLinkedParentAssetId(ctx, parentAssetId)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (ah *AssetTyreHandler) UpdateAssetTyre(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")
	var req dtos.UpdateAssetTyreReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	resp, err := ah.AssetTyreUseCase.UpdateAssetTyreV2(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetTyreList(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.TyreListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()

	resp, err := h.AssetTyreUseCase.GetTyreList(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetTyreSizeList(c *gin.Context) {
	ctx := c.Request.Context()
	req := commonmodel.ListRequest{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetTyreSizeList(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetTyrePatternTypes(c *gin.Context) {
	ctx := c.Request.Context()
	req := commonmodel.ListRequest{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetTyrePatternTypes(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) CreateTyre(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateTyreReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.CreateTyre(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetTyreHandler) DeleteTyre(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Param("id")
	resp, err := h.AssetTyreUseCase.DeleteTyre(ctx, ID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetTyre(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Param("id")
	resp, err := h.AssetTyreUseCase.GetTyre(ctx, ID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) UpdateTyre(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Param("id")

	var req dtos.UpdateTyreReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.UpdateTyre(ctx, ID, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) RetreadAssetTyre(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("id")

	var req dtos.RetreadAssetTyreReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.RetreadAssetTyre(ctx, assetID, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetTyreHandler) UpdateRetreadAssetTyre(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Param("id")

	var req dtos.RetreadAssetTyreReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.UpdateRetreadAssetTyre(ctx, ID, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) BulkUploadAssetTyres(c *gin.Context) {
	ctx := c.Request.Context()
	var req commonmodel.BulkUploadReq
	err := c.Bind(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	fileName := req.FileHeader.Filename

	data, oriFileBytes, err := h.AssetTyreUseCase.ParseAssetTyreBulkUploadV2(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	resp, err := h.AssetTyreUseCase.AssetTyreBulkUploadV2(ctx, fileName, oriFileBytes, data)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetTyreHandler) GetTyreCSV(c *gin.Context) {
	time := time.Now().Format("02012006")
	filename := fmt.Sprintf(`attachment; filename="tyre_%s.csv"`, time)
	c.Writer.Header().Set("Content-Type", "text/csv")
	c.Writer.Header().Set("Content-Disposition", filename)
	ctx := c.Request.Context()
	resp, err := h.AssetTyreUseCase.GetTyresCSV(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}
	err = gocsv.Marshal(&resp, c.Writer)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}
	c.Status(http.StatusOK)
}

func (h *AssetTyreHandler) PopulatePeriodicAssetTyreStatsHistories(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.AssetTyreUseCase.PopulatePeriodicAssetTyreStatsHistories(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetLastMonthAssetTyreStatsHistory(c *gin.Context) {
	ctx := c.Request.Context()

	assetID := c.Param("asset_id")
	resp, err := h.AssetTyreUseCase.GetLastMonthAssetTyreStatsHistory(ctx, assetID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreScrappedList(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreScrappedDisposedListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	recipesResponse, err := h.AssetTyreUseCase.GetAssetTyreScrappedList(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, recipesResponse)
}

func (h *AssetTyreHandler) ExportAssetTyreByID(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("id")

	resp, err := h.AssetTyreUseCase.ExportAssetTyreByID(ctx, assetID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *AssetTyreHandler) GetAssetTyreDisposedList(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreScrappedDisposedListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	recipesResponse, err := h.AssetTyreUseCase.GetAssetTyreDisposedList(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, recipesResponse)
}

func (h *AssetTyreHandler) GetAssetTyresTreadConfigs(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyresTreadConfigRequest{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyresTreadConfigList(ctx, req)
	if err != nil {
		commonlogger.Errorf("Failed in getting asset tyres retread configs ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in getting asset tyres retread configs"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyresTreadConfigByID(c *gin.Context) {
	ctx := c.Request.Context()
	configID := c.Param("id")

	resp, err := h.AssetTyreUseCase.GetAssetTyresTreadConfigByID(ctx, configID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) CreateAssetTyresTreadConfig(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateOrUpdateAssetTyresTreadConfigRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.CreateAssetTyresTreadConfig(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssetTyreHandler) UpdateAssetTyresTreadConfig(c *gin.Context) {
	ctx := c.Request.Context()
	configID := c.Param("id")

	var req dtos.CreateOrUpdateAssetTyresTreadConfigRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.UpdateAssetTyresTreadConfig(ctx, configID, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) DeleteAssetTyresTreadConfig(c *gin.Context) {
	ctx := c.Request.Context()
	configID := c.Param("id")

	resp, err := h.AssetTyreUseCase.DeleteAssetTyresTreadConfig(ctx, configID)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyresTreadConfigBrands(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyresTreadConfigRequest{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyresTreadConfigBrands(ctx, req)
	if err != nil {
		commonlogger.Errorf("Failed in getting asset tyres retread config brands ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed in getting asset tyres retread config brands"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreStockReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreStockReportReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyreStockReport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetUtilizationRatePercentageStatus(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.AssetTyreUseCase.GetUtilizationRatePercentageStatus(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreStockReportExport(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetTyreUseCase.GetAssetTyreStockReportExport(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreInstalledReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreInstalledReportReq{}
	err := c.BindQuery(&req)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyreInstalledReport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreInstalledReportExport(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetTyreUseCase.GetAssetTyreInstalledExport(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreUninstalledReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreUninnstalledReportReq{}
	err := c.BindQuery(&req)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyreUninstalledReport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreUninstalledReportExport(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetTyreUseCase.GetAssetTyreUninstalledExport(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreReplacementForecastReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreReplacementForecastReportReq{}
	err := c.BindQuery(&req)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyreReplacementForecastReport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreReplacementForecastExport(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetTyreUseCase.GetAssetTyreReplacementForecastExport(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreUsageReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreUsageReportReq{}
	err := c.BindQuery(&req)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyreUsageReport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreUsageReportExport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreUsageReportReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.GetAssetTyreUsageReportExport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreInspectionReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreInspectionReportReq{}
	err := c.BindQuery(&req)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyreInspectionReport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreInspectionReportExport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreInspectionReportReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.GetAssetTyreInspectionReportExport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreRotationReport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreRotationReportReq{}
	err := c.BindQuery(&req)

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.AssetTyreUseCase.GetAssetTyreRotationReport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetTyreHandler) GetAssetTyreRotationReportExport(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.GetAssetTyreRotationReportReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.AssetTyreUseCase.GetAssetTyreRotationReportExport(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}
