package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
	// "context"
)

type AssetRepository interface {
	GetAssetList(ctx context.Context, dB database.DBI, param models.GetAssetListParam) (int, []models.Asset, error)
	GetAssetSelectionsList(ctx context.Context, dB database.DBI, param models.GetAssetListParam) (int, []models.Asset, error)

	CreateAsset(ctx context.Context, dB database.DBI, assets *models.Asset) error
	IsSerialNumberExist(ctx context.Context, dB database.DBI, serialNumber string, clientID string) (bool, error)
	GetAssetByID(ctx context.Context, dB database.DBI, id string) (*models.Asset, error)
	GetAsset(ctx context.Context, dB database.DBI, condition models.AssetCondition) (*models.Asset, error)
	GetAssets(ctx context.Context, dB database.DBI, condition models.AssetCondition) ([]models.Asset, error)
	GetAssetsByIDs(ctx context.Context, dB database.DBI, ids []string) ([]models.Asset, error)
	GetAssetDataInformation(ctx context.Context, dB database.DBI, assetId string) ([]byte, error)

	GetAssetBrands(ctx context.Context, dB database.DBI, brands *[]models.Brand, brandTags []string) error

	UpdateAsset(ctx context.Context, dB database.DBI, asset *models.Asset) error
	UpdateAssetForUpdateWithNull(ctx context.Context, dB database.DBI, id string, asset *models.AssetForUpdateWithNull) error
	UpdateAssetStatusCode(ctx context.Context, dB database.DBI, id string, assetStatusCode string) error
	UpdateAssetStatusByIDs(ctx context.Context, dB database.DBI, ids []string, assetStatusCode string) error
	UpdateAssetPartnerOwnerName(ctx context.Context, dB database.DBI, partnerID string, newName string) error
	GetAssetStatusByCode(ctx context.Context, dB database.DBI, code string) (*models.AssetStatus, error)

	GetAssetManagementList(ctx context.Context, dB database.DBI, param models.GetAssetListParam) (int, []models.Asset, error)
	GetAssetManagement(ctx context.Context, dB database.DBI, cond models.AssetCondition) (*models.Asset, error)
	GetAssetCategory(ctx context.Context, dB database.DBI, cond models.AssetCategoryCondition) (*models.AssetCategory, error)
	GetAssetCategories(ctx context.Context, dB database.DBI, cond models.AssetCategoryCondition) ([]models.AssetCategory, error)
	GetAssetCategoryMapping(ctx context.Context, dB database.DBI, cond models.AssetCategoryMappingCondition) (*models.AssetCategoryMapping, error)
	GetAssetCategoryMappings(ctx context.Context, dB database.DBI, cond models.AssetCategoryMappingCondition) ([]models.AssetCategoryMapping, error)

	GetAssetSubCategory(ctx context.Context, dB database.DBI, cond models.AssetSubCategoryCondition) (*models.AssetSubCategory, error)

	CountAsset(ctx context.Context, dB database.DBI, condition models.AssetCondition) (int, error)

	// Chart Asset
	ChartCountAssets(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartCountInstalledTyreNonSpare(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartAssetStatus(ctx context.Context, dB database.DBI, clientID string, statusCodes []string) ([]commonmodel.Chart, error)
	ChartAssetBrands(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartNotSetAssetBrands(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartAssetCustomCategories(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartNotSetAssetCustomCategories(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartAssetCustomCategoriesSubcategories(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartNotSetAssetCustomCategoriesSubcategories(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartAssetExpiredComponent(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)

	// Chart Tyre
	ChartTyreStatus(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartTyreTread(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
	ChartTyreSize(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.ChartBar, error)
	ChartTyreBrand(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error)
}
