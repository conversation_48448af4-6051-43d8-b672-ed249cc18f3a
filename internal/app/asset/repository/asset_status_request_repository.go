package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type AssetStatusRequestRepository interface {
	CreateAssetStatusRequest(ctx context.Context, dB database.DBI, assetStatusRequest *models.AssetStatusRequest) error
	CreateAssetStatusRequests(ctx context.Context, dB database.DBI, assetStatusRequests []models.AssetStatusRequest) error
	GetAssetStatusRequest(ctx context.Context, dB database.DBI, condition models.AssetStatusRequestCondition) (*models.AssetStatusRequest, error)
	GetAssetStatusRequests(ctx context.Context, dB database.DBI, condition models.AssetStatusRequestCondition) ([]models.AssetStatusRequest, error)
	GetAssetStatusRequestByAssetId(ctx context.Context, dB database.DBI, condition models.GetAssetStatusRequestListParam) (int, []models.AssetStatusRequest, error)
	GetAssetStatusRequestReason(ctx context.Context, dB database.DBI, assetCategoryCode string) ([]*models.AssetStatusRequestReason, error)
	UpdateAssetstatusRequest(ctx context.Context, dB database.DBI, id string, assetStatusRequest *models.AssetStatusRequest) error
	GetAssetStatusRequestsByIDs(ctx context.Context, dB database.DBI, ids []string) ([]models.AssetStatusRequest, error)
	GetAssetStatusRequestGrades(ctx context.Context, dB database.DBI) ([]models.AssetStatusRequestGrade, error)

	GetAssetStatusRequestReasonByCode(ctx context.Context, dB database.DBI, code string) (*models.AssetStatusRequestReason, error)
}
