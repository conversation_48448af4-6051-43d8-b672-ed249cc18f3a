package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"errors"

	"gorm.io/gorm"
)

type AssetStatusRequestRepository struct{}

func NewAssetStatusRequestRepository() repository.AssetStatusRequestRepository {
	return &AssetStatusRequestRepository{}
}

func (r *AssetStatusRequestRepository) CreateAssetStatusRequest(ctx context.Context, dB database.DBI, assetStatusRequest *models.AssetStatusRequest) error {
	model := models.AssetStatusRequest{}
	err := dB.GetTx().Model(&model).Create(assetStatusRequest).Error
	if err != nil {
		return err
	}
	return nil
}

func (r *AssetStatusRequestRepository) CreateAssetStatusRequests(ctx context.Context, dB database.DBI, assetStatusRequests []models.AssetStatusRequest) error {
	return dB.GetTx().Create(&assetStatusRequests).Error
}

func (r *AssetStatusRequestRepository) UpdateAssetstatusRequest(ctx context.Context, dB database.DBI, id string, assetStatusRequest *models.AssetStatusRequest) error {
	return dB.GetTx().
		Model(&models.AssetStatusRequest{}).
		Where("id = ?", id).
		Updates(assetStatusRequest).
		Error
}
func (r *AssetStatusRequestRepository) GetAssetStatusRequest(ctx context.Context, dB database.DBI, condition models.AssetStatusRequestCondition) (*models.AssetStatusRequest, error) {
	var result *models.AssetStatusRequest
	model := models.AssetStatusRequest{}
	query := dB.GetTx().Model(&model)
	enrichAssetStatusRequestQueryWithWhere(query, condition.Where)

	err := query.First(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errorhandler.ErrDataNotFound("asset status request")
		}
		return nil, err
	}
	return result, nil
}
func (r *AssetStatusRequestRepository) GetAssetStatusRequestByAssetId(ctx context.Context, dB database.DBI, condition models.GetAssetStatusRequestListParam) (int, []models.AssetStatusRequest, error) {
	results := []models.AssetStatusRequest{}
	model := models.AssetStatusRequest{}
	query := dB.GetTx().Model(&model)
	query.Where("asset_id = ? AND status_code != 'REJECTED' AND client_id = ?", condition.Cond.Where.AssetID, condition.Cond.Where.ClientID)
	enrichAssetStatusRequestQueryWithWhere(query, condition.Cond.Where)

	err := query.Find(&results).Error
	if err != nil {
		return 1, results, err
	}
	return len(results), results, nil
}
func (r *AssetStatusRequestRepository) GetAssetStatusRequestReason(ctx context.Context, dB database.DBI, assetCategoryCode string) ([]*models.AssetStatusRequestReason, error) {
	results := make([]*models.AssetStatusRequestReason, 0)
	model := models.AssetStatusRequestReason{}
	err := dB.GetOrm().Model(&model).
		Preload("AssetCategory").
		Preload("SubReason", func(db *gorm.DB) *gorm.DB {
			return db.Order("label ASC")
		}).
		Order("label ASC").
		Where("asset_category_code = ?", assetCategoryCode).
		Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (r *AssetStatusRequestRepository) GetAssetStatusRequestReasonByCode(ctx context.Context, dB database.DBI, code string) (*models.AssetStatusRequestReason, error) {
	results := &models.AssetStatusRequestReason{}
	err := dB.GetOrm().Model(&models.AssetStatusRequestReason{}).Where("code = ?", code).First(&results).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset status request reason")
		}
		return nil, err
	}

	return results, nil
}

func (r *AssetStatusRequestRepository) GetAssetStatusRequestsByIDs(ctx context.Context, dB database.DBI, ids []string) ([]models.AssetStatusRequest, error) {
	var assetStatusRequests []models.AssetStatusRequest
	err := dB.GetTx().
		Where("id IN ?", ids).
		Find(&assetStatusRequests).Error

	if err != nil {
		return nil, err
	}
	return assetStatusRequests, nil
}

func (r *AssetStatusRequestRepository) GetAssetStatusRequestGrades(ctx context.Context, dB database.DBI) ([]models.AssetStatusRequestGrade, error) {
	assetStatusRequestGrade := []models.AssetStatusRequestGrade{}
	query := dB.GetTx().Model(&assetStatusRequestGrade)
	err := query.Find(&assetStatusRequestGrade).Error
	if err != nil {
		return nil, err
	}

	return assetStatusRequestGrade, nil
}

func enrichAssetStatusRequestQueryWithWhere(query *gorm.DB, where models.AssetStatusRequestWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	}

	if len(where.IDs) > 0 {
		query.Where("id IN ?", where.IDs)
	}

	if where.TypeCode != "" {
		query.Where("type_code = ?", where.TypeCode)
	}

	if where.AssetID != "" {
		query.Where("asset_id = ?", where.AssetID)
	}

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	}

	if where.StatusCode != "" {
		query.Where("status_code = ?", where.StatusCode)
	}

	if len(where.StatusCodes) > 0 {
		query.Where("status_code IN ?", where.StatusCodes)
	}
}

func (r *AssetStatusRequestRepository) GetAssetStatusRequests(ctx context.Context, dB database.DBI, condition models.AssetStatusRequestCondition) ([]models.AssetStatusRequest, error) {
	var result []models.AssetStatusRequest
	query := dB.GetTx().Model(&models.AssetStatusRequest{})
	enrichAssetStatusRequestQueryWithWhere(query, condition.Where)
	err := query.Find(&result).Error
	if err != nil {
		return nil, err
	}

	return result, nil
}
