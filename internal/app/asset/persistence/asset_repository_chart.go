package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
	"strconv"
	"time"

	"gopkg.in/guregu/null.v4"
)

func (ar *AssetRepository) ChartCountAssets(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
	})

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(total),
			Name: "Total Assets",
		},
	}, nil
}

func (ar *AssetRepository) ChartAssetStatus(ctx context.Context, dB database.DBI, clientID string, statusCodes []string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
		Statuses:           statusCodes,
	})

	query.Joins(`JOIN "ams_ASSET_STATUSES" 
		   ON "ams_ASSET_STATUSES".code = ams_assets.asset_status_code`)

	query.Group(`ams_assets.asset_status_code, "ams_ASSET_STATUSES".label`)

	query.Select(
		"count(ams_assets.id) AS y",
		"ams_assets.asset_status_code AS code",
		`"ams_ASSET_STATUSES".label AS name`,
	)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetRepository) ChartAssetBrands(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
	})

	query.Joins(`JOIN "ams_brands" 
		   ON "ams_brands".id = ams_assets.brand_id`)

	query.Group(`ams_assets.brand_id, "ams_brands".brand_name`)

	query.Select(
		"count(ams_assets.id) AS y",
		"ams_assets.brand_id AS code",
		`"ams_brands".brand_name AS name`,
	)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetRepository) ChartNotSetAssetBrands(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
	})

	query.Select(
		"count(ams_assets.id) AS y",
		"'NOT_SET' AS code",
		"'Not Set' AS name",
	).Where("ams_assets.brand_id IS NULL")

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetRepository) ChartAssetCustomCategories(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
	})

	query.Joins(`JOIN "ams_custom_asset_categories" 
		   ON "ams_custom_asset_categories".id = ams_assets.custom_asset_category_id`)

	query.Group(`ams_assets.custom_asset_category_id, "ams_custom_asset_categories".name`)

	query.Select(
		"count(ams_assets.id) AS y",
		"ams_assets.custom_asset_category_id AS code",
		`"ams_custom_asset_categories".name AS name`,
	)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetRepository) ChartNotSetAssetCustomCategories(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
	})

	query.Select(
		"count(ams_assets.id) AS y",
		"'NOT_SET' AS code",
		"'Not Set' AS name",
	).Where("ams_assets.custom_asset_category_id IS NULL")

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetRepository) ChartAssetCustomCategoriesSubcategories(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
	})
	query.Joins(`JOIN "ams_custom_asset_sub_categories" 
		ON "ams_custom_asset_sub_categories".id = ams_assets.custom_asset_sub_category_id`)

	query.Group(`ams_assets.custom_asset_sub_category_id, "ams_custom_asset_sub_categories".name, ams_assets.custom_asset_category_id`)

	query.Select(
		"count(ams_assets.id) AS y",
		"ams_assets.custom_asset_sub_category_id AS code",
		`"ams_custom_asset_sub_categories".name AS name`,
		"ams_assets.custom_asset_category_id AS parent_code",
	)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetRepository) ChartNotSetAssetCustomCategoriesSubcategories(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
	})

	query.Joins(`JOIN "ams_custom_asset_categories" 
		   ON "ams_custom_asset_categories".id = ams_assets.custom_asset_category_id`)

	query.Group(`ams_assets.custom_asset_category_id, "ams_custom_asset_categories".name`)

	query.Select(
		"count(ams_assets.id) AS y",
		"ams_assets.custom_asset_category_id AS code",
		`"ams_custom_asset_categories".name AS name`,
		"ams_assets.custom_asset_category_id AS parent_code",
	).Where("ams_assets.custom_asset_sub_category_id IS NULL")

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

// Tyre Chart Repo

func (ar *AssetRepository) ChartTyreStatus(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:   clientID,
		Categories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
		ExcludedStatuses: []string{
			constants.ASSET_STATUS_CODE_DISPOSED,
			constants.ASSET_STATUS_CODE_SCRAPED,
		},
	})

	query.Joins(`JOIN "ams_ASSET_STATUSES" 
		   ON "ams_ASSET_STATUSES".code = ams_assets.asset_status_code`)

	query.Group(`ams_assets.asset_status_code, "ams_ASSET_STATUSES".label`)

	query.Select(
		"count(ams_assets.id) AS y",
		"ams_assets.asset_status_code AS code",
		`"ams_ASSET_STATUSES".label AS name`,
	)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetRepository) ChartTyreTread(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:   clientID,
		Categories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
		ExcludedStatuses: []string{
			constants.ASSET_STATUS_CODE_DISPOSED,
			constants.ASSET_STATUS_CODE_SCRAPED,
		},
	})

	query.Joins(`JOIN "ams_asset_tyres"
				ON ams_assets.id = "ams_asset_tyres".asset_id
	`)

	query.Group(`ams_asset_tyres.retread_number`)

	query.Select(
		"COUNT(1) AS num",
		"retread_number",
	)

	type RetreadStats struct {
		Num           int
		RetreadNumber int
	}

	var result []RetreadStats
	err := query.Scan(&result).Error
	if err != nil {
		return nil, err
	}

	res := make([]commonmodel.Chart, 0, len(result))
	for _, r := range result {
		name := "Original"
		if r.RetreadNumber > 0 {
			name = "Retread " + strconv.Itoa(r.RetreadNumber)
		}
		res = append(res, commonmodel.Chart{
			Name: name,
			Y:    float64(r.Num),
		})
	}

	return res, nil
}

func (ar *AssetRepository) ChartTyreSize(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.ChartBar, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:   clientID,
		Categories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
		ExcludedStatuses: []string{
			constants.ASSET_STATUS_CODE_DISPOSED,
			constants.ASSET_STATUS_CODE_SCRAPED,
		},
	})
	query.Joins(`JOIN "ams_asset_tyres"
				ON ams_assets.id = "ams_asset_tyres".asset_id
	`)
	query.Joins(`JOIN "ams_tyres"
				ON ams_asset_tyres.tyre_id = "ams_tyres".id
	`)

	query.Select(
		"concat(ams_tyres.section_width ,' ', ams_tyres.construction,' ',ams_tyres.rim_diameter) as x",
		"count(*) as y",
	)

	query.Group(`concat(ams_tyres.section_width ,' ', ams_tyres.construction,' ',ams_tyres.rim_diameter)`).
		Order("y DESC").
		Limit(10)

	var charts []commonmodel.ChartBar
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetRepository) ChartTyreBrand(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:   clientID,
		Categories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
		ExcludedStatuses: []string{
			constants.ASSET_STATUS_CODE_DISPOSED,
			constants.ASSET_STATUS_CODE_SCRAPED,
		},
	})

	query.Joins(`JOIN "ams_brands" 
		   ON "ams_brands".id = ams_assets.brand_id`)

	query.Group(`ams_assets.brand_id, "ams_brands".brand_name`)

	query.Select(
		"count(ams_assets.id) AS y",
		"ams_assets.brand_id AS code",
		`"ams_brands".brand_name AS name`,
	).
		Order("y DESC").
		Limit(10)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}
	return charts, nil
}

func (ar *AssetRepository) ChartAssetExpiredComponent(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	var count int64
	query := dB.GetTx().Model(&models.Asset{})

	enrichAssetQueryWithWhere(query, models.AssetWhere{
		ClientID:           clientID,
		ExcludedCategories: []string{constants.ASSET_CATEGORY_TYRE_CODE},
	})

	query.Joins("JOIN ams_asset_components ON ams_asset_components.asset_id = ams_assets.id").
		Where("ams_asset_components.expiry_date < ?", time.Now()).
		Select("DISTINCT ams_assets.id")

	err := query.Count(&count).Error
	if err != nil {
		return nil, err
	}

	var charts []commonmodel.Chart
	expiredComponentChart := commonmodel.Chart{
		Name: "Assets With Expired Component",
		Code: null.StringFrom("ASSETS_WITH_EXPIRED_COMPONENT"),
		Y:    float64(count),
	}
	charts = append(charts, expiredComponentChart)

	return charts, nil
}

func (ar *AssetRepository) ChartCountInstalledTyreNonSpare(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.Asset{})

	// Filter by client ID, tyre category, and installed status
	query = query.Where("ams_assets.client_id = ?", clientID).
		Where("ams_assets.asset_category_code = ?", constants.ASSET_CATEGORY_TYRE_CODE).
		Where("ams_assets.asset_status_code = ?", constants.ASSET_STATUS_CODE_INSTALLED)

	// Join with asset_tyres to access is_spare field
	query = query.Joins("JOIN ams_asset_tyres ON ams_asset_tyres.asset_id = ams_assets.id").
		Where("ams_asset_tyres.is_spare = ?", false)

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(total),
			Name: "Total Registered Running Tyres (Exc. Spares)",
		},
	}, nil
}
