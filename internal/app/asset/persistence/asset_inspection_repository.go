package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
	"database/sql"
	"strconv"
	"strings"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AssetInspectionRepository struct{}

func enrichAssetInspectionQueryWithWhere(query *gorm.DB, where models.AssetInspectionWhere) {
	if where.ID != "" {
		query.Where("ams_asset_inspections.id = ?", where.ID)
	}

	if where.ClientID != "" {
		query.Where("ams_asset_inspections.client_id = ?", where.ClientID)
	}

	if where.StartDate != "" {
		query.Where("ams_asset_inspections.created_at >= ?", where.StartDate)
	} // StartDate

	if !where.StartDate1.IsZero() {
		query.Where("ams_asset_inspections.created_at >= ?", where.StartDate1)
	} // StartDate1

	if where.EndDate != "" {
		query.Where("ams_asset_inspections.created_at < ?", where.EndDate)
	} // EndDate

	if !where.EndDate1.IsZero() {
		query.Where("ams_asset_inspections.created_at < ?", where.EndDate1)
	} // EndDate1

	if where.ReferenceCode != "" {
		query.Where("reference_code = ? AND reference_id = ?", where.ReferenceCode, where.ReferenceID)
	}

	if where.ReferenceCodeExclude != "" {
		query.Where("reference_code != ?", where.ReferenceCode)
	}

	if where.IsAssignToUserLogin {
		query.Joins("LEFT JOIN ams_asset_inspection_assignments ON ams_asset_inspections.id=ams_asset_inspection_assignments.inspection_id")
		query.Where("ams_asset_inspection_assignments.user_id = ?", where.UserID)
	}

	if where.HasVehicleInspection != nil {
		hasVehicleInspection := strconv.FormatBool(*where.HasVehicleInspection)
		if hasVehicleInspection == "true" {
			query.Where("ams_asset_inspection_vehicle.id IS NOT NULL")
		} else {
			query.Where("ams_asset_inspection_vehicle.id IS NULL")
		}
	}

	if where.HasTyreOptimaxInspection != nil {
		hasTyreOptimaxInspection := strconv.FormatBool(*where.HasTyreOptimaxInspection)
		if hasTyreOptimaxInspection == "true" {
			query.Where("ams_asset_inspection_tyre.asset_tyre_id IS NOT NULL")
		} else {
			query.Where("ams_asset_inspection_tyre.asset_tyre_id IS NULL")
		}
	}

	if where.HasTyreInspection != nil {
		hasTyreInspection := strconv.FormatBool(*where.HasTyreInspection)
		if hasTyreInspection == "true" {
			query.Where("ams_asset_inspection_tyre.id IS NOT NULL")
		} else {
			query.Where("ams_asset_inspection_tyre.id IS NULL")
		}
	}

	if len(where.StatusCodes) > 0 {
		query.Where("status_code IN ?", where.StatusCodes)
	}

	if len(where.TyreSourceCodes) > 0 {
		query.Where("ams_asset_inspection_tyre.device_id IS NOT NULL")
	}

	if len(where.InspectByUserIDs) > 0 {
		query.Where("ams_asset_inspections.inspect_by_user_id IN ?", where.InspectByUserIDs)
	} // InspectByUserIDs

	if where.InspectByUserID != "" {
		query.Where("ams_asset_inspections.inspect_by_user_id = ?", where.InspectByUserID)
	} // InspectByUserID

	if where.DigiSpectOnlySource {
		query.Where("ams_asset_inspection_tyre.source_type_code IN @arg OR ams_asset_inspection_vehicle.source_type_code IN @arg", sql.Named("arg", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		}))
	} // DigiSpectOnlySource

	if where.Limit > 0 {
		query.Limit(where.Limit)
	}

	if where.DigiSpectVehicleID != "" {
		query.Where("ams_asset_inspection_vehicle.digispect_vehicle_id = ?", where.DigiSpectVehicleID)
	}

	if len(where.PartnerOwnerNames) > 0 {
		query.Where("ams_asset_inspection_vehicle.partner_owner_name IN ?", where.PartnerOwnerNames)
	}

	if len(where.CustomReferenceNumbers) > 0 {
		query.Where("ams_asset_inspection_vehicle.custom_reference_number IN ?", where.CustomReferenceNumbers)
	}

	if len(where.CustomBrandNames) > 0 {
		query.Where("ams_asset_inspection_vehicle.custom_brand_name IN ?", where.CustomBrandNames)
	}

	if len(where.CustomModelNames) > 0 {
		query.Where("ams_asset_inspection_vehicle.custom_model_name IN ?", where.CustomModelNames)
	}

	if where.IsSingleTyreInspection {
		query.Where("ams_asset_inspection_tyre.id IS NOT NULL AND ams_asset_inspection_vehicle.id IS NULL")
	}

	if where.IsLinkedTyreInspection {
		query.Where("ams_asset_inspection_tyre.id IS NOT NULL AND ams_asset_inspection_vehicle.id IS NOT NULL")
	}
}

func enrichAssetInspectionQueryWithPreload(query *gorm.DB, preload models.AssetInspectionPreload) {
	if preload.Reference {
		query.Preload("Reference")
	} // Reference

	if preload.CompleteInspectionItemDetail {
		query.Preload("AssetInspectionTyre").
			Preload("AssetInspectionTyre.AssetTyre").
			Preload("AssetInspectionTyre.AssetTyre.Tyre").
			Preload("AssetInspectionTyre.AssetTyre.Asset").
			Preload("AssetInspectionTyre.AssetTyre.Asset.Brand")

		query.Preload("AssetInspectionVehicle").
			Preload("AssetInspectionVehicle.AssetVehicle").
			Preload("AssetInspectionVehicle.AssetVehicle.Vehicle").
			Preload("AssetInspectionVehicle.AssetVehicle.Asset").
			Preload("AssetInspectionVehicle.AssetVehicle.Asset.Brand")
	} // Complete

	if preload.CountInspectionTyres {
		query.Preload("CountInspectionTyres", func(db *gorm.DB) *gorm.DB {
			return db.Select("count(*) AS number_inspection_tyre", "asset_inspection_id").Group("asset_inspection_id")
		})
	}

	if preload.AssetInspectionTyres {
		query.Preload("AssetInspectionTyres")
	}

	if preload.AssetInspectionVehicle {
		query.Preload("AssetInspectionVehicle")
	}
}

func (r *AssetInspectionRepository) GetAssetInspectionList(ctx context.Context, dB database.DBI, param models.GetAssetInspectionListParam) (int, []models.AssetInspection, error) {
	var totalRecords int64
	assetInspections := []models.AssetInspection{}
	query := dB.GetTx().Model(&assetInspections)

	query.Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id")
	query.Joins("LEFT JOIN ams_asset_inspection_tyre ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id")

	query.Joins("LEFT JOIN ams_assets ON (ams_assets.id = ams_asset_inspection_vehicle.asset_vehicle_id OR ams_assets.id = ams_asset_inspection_tyre.asset_tyre_id)")
	enrichAssetInspectionQueryWithWhere(query, param.Cond.Where)
	enrichAssetInspectionQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_asset_inspections.inspection_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_inspection_tyre.custom_serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_inspection_vehicle.custom_reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_inspection_vehicle.custom_brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_inspection_tyre.custom_brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_inspection_vehicle.custom_model_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_asset_inspection_vehicle.partner_owner_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	query.Group("ams_asset_inspections.id")
	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	if len(param.Sorts) > 0 {
		for _, sort := range param.Sorts {
			s := strings.Split(sort, ":")
			if len(s) < 2 {
				continue
			}

			if _, ok := models.AssetInspectionSorts[s[0]]; !ok {
				continue
			}

			query.
				Select(
					"ams_asset_inspections.*, " +
						"COUNT(ams_asset_inspection_tyre.id) AS number_of_inspection_point, " +
						"MAX(COALESCE(ams_asset_inspection_vehicle.vehicle_hm, 0)) AS vehicle_hm, " +
						"MAX(COALESCE(ams_asset_inspection_vehicle.vehicle_km, 0)) AS vehicle_km, " +
						"GREATEST(MAX(COALESCE(ams_asset_inspection_vehicle.vehicle_km, 0)), MAX(COALESCE(ams_asset_inspection_vehicle.vehicle_hm, 0))) AS vehicle_km_hm",
				)

			if s[0] == "created_at" {
				query.Order("date_trunc('second', ams_asset_inspections.created_at) " + s[1])
			} else {
				query.Order(s[0] + " " + s[1])
			}
		}
	} else {
		query.Order("ams_asset_inspections.updated_at DESC")
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assetInspections).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assetInspections, nil
}

func (r *AssetInspectionRepository) GetAssetInspectionByAssignedUserId(ctx context.Context, dB database.DBI, userId string, referenceCode string) ([]models.AssetInspection, error) {
	assetInspections := []models.AssetInspection{}
	query := dB.GetTx().Model(&assetInspections)

	err := query.
		Joins("JOIN ams_asset_inspection_assignments ON ams_asset_inspection_assignments.inspection_id = ams_asset_inspections.id").
		Where("ams_asset_inspections.reference_id IS NOT NULL").
		Where("ams_asset_inspections.reference_code = ?", referenceCode).
		Where("ams_asset_inspection_assignments.user_id = ?", userId).
		Group("ams_asset_inspections.id").
		Group("ams_asset_inspections.reference_id").
		Order("ams_asset_inspections.updated_at DESC").
		Limit(100).
		Find(&assetInspections).
		Error
	if err != nil {
		return nil, err
	}

	return assetInspections, nil
}

func NewAssetInspectionRepository() repository.AssetInspectionRepository {
	return &AssetInspectionRepository{}
}

func (r *AssetInspectionRepository) CreateAssetInspection(ctx context.Context, dB database.DBI, assetInspection *models.AssetInspection) error {
	return dB.GetTx().Create(assetInspection).Error
}

func (r *AssetInspectionRepository) UpdateAssetInspection(ctx context.Context, dB database.DBI, id string, assetInspection *models.AssetInspection) error {
	return dB.GetTx().
		Model(&models.AssetInspection{}).
		Where("id = ?", id).
		Updates(assetInspection).
		Error
}

func (ar *AssetInspectionRepository) UpsertAssetInspectionAssignment(ctx context.Context, dB database.DBI, assetInspectionAssignment *models.AssetInspectionAssignment) error {
	return dB.GetTx().Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "inspection_id"}, {Name: "type_code"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"updated_at",
			"user_id",
			"user_name",
		}),
	}).Create(assetInspectionAssignment).Error
}

func (ar *AssetInspectionRepository) DoneInspectionByReferenceID(ctx context.Context, dB database.DBI, referenceID string) error {
	return dB.GetTx().
		Model(&models.AssetInspection{}).
		Where("reference_id = ?", referenceID).
		Updates(&models.AssetInspection{
			StatusCode: constants.ASSET_INSPECTION_STATUS_CODE_DONE,
		}).
		Error
}

func enrichAssetInspectionAssignmentQueryWithWhere(query *gorm.DB, where models.AssetInspectionAssignmentWhere) {
	// RequesterUserID:     claim.UserID,
	// 		AssignedToUserID:    claim.UserID,
	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.InspectionID != "" {
		query.Where("inspection_id = ?", where.InspectionID)
	} // TicketID

	if where.TypeCode != "" {
		query.Where("type_code = ?", where.TypeCode)
	} // TypeCode
}

func (r *AssetInspectionRepository) GetAssetInspectionAssignments(ctx context.Context, dB database.DBI, condition models.AssetInspectionAssignmentCondition) ([]models.AssetInspectionAssignment, error) {
	assetInspectionAssignment := []models.AssetInspectionAssignment{}
	query := dB.GetTx().Model(&assetInspectionAssignment)
	enrichAssetInspectionAssignmentQueryWithWhere(query, condition.Where)

	err := query.Find(&assetInspectionAssignment).Error
	if err != nil {
		return nil, err
	}

	return assetInspectionAssignment, nil
}

func (r *AssetInspectionRepository) GetAssetInspection(ctx context.Context, dB database.DBI, cond models.AssetInspectionCondition) (*models.AssetInspection, error) {
	location := models.AssetInspection{}
	query := dB.GetOrm().Model(&location)

	query.Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id")
	query.Joins("LEFT JOIN ams_asset_inspection_tyre ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, cond.Where)
	enrichAssetInspectionQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	query.Order("ams_asset_inspections.created_at DESC")
	err := query.First(&location).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("ASSET_INSPECTION")
		}

		return nil, err
	}

	return &location, nil
}

func (r *AssetInspectionRepository) GetAssetInspections(ctx context.Context, dB database.DBI, cond models.AssetInspectionCondition) ([]models.AssetInspection, error) {
	location := []models.AssetInspection{}
	query := dB.GetOrm().Model(&models.AssetInspection{})

	query.Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id")
	query.Joins("LEFT JOIN ams_asset_inspection_tyre ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, cond.Where)
	enrichAssetInspectionQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	query.Order("created_at DESC")
	query.Group("ams_asset_inspections.id")

	err := query.Find(&location).Error
	if err != nil {
		return nil, err
	}

	return location, nil
}

func (ar *AssetInspectionRepository) ChartCountSingleAndLinkedInspections(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspection{})

	query.Joins("LEFT JOIN ams_asset_inspection_tyre ON ams_asset_inspection_tyre.asset_inspection_id = ams_asset_inspections.id")
	query.Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspection_vehicle.asset_inspection_id = ams_asset_inspections.id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:            req.ClientID,
		StartDate1:          req.StartDatetime,
		EndDate1:            req.EndDatetime,
		DigiSpectOnlySource: req.IsFromDigiSpect,
	})

	query.Select(`CASE WHEN ams_asset_inspection_vehicle.id IS NULL AND ams_asset_inspection_tyre.id IS NOT NULL THEN 'SINGLE_TYRE'
	WHEN ams_asset_inspection_vehicle.id IS NOT NULL THEN 'LINKED_TYRE' END AS code`, "ams_asset_inspections.id")

	mainQuery := dB.GetTx().Table("(?) AS temp", query)

	mainQuery.Group("id, code")
	mainQuery.Select(
		"code",
		"id",
	)

	qQuery := dB.GetTx().Table("(?) AS temp2", mainQuery)

	qQuery.Group("code")
	qQuery.Select(
		"code",
		`CASE code
			WHEN 'SINGLE_TYRE' THEN 'Single Tyre'
			WHEN 'LINKED_TYRE' THEN 'Linked Tyre'
		END AS name`,
		"COUNT(*) AS y",
	)

	var charts []commonmodel.Chart
	if err := qQuery.Scan(&charts).Error; err != nil {
		return nil, err
	}

	return charts, nil

}

func (ar *AssetInspectionRepository) ChartVehicleInspectionFrequency(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	// First, create a subquery to count inspections per vehicle
	subQuery := dB.GetTx().Model(&models.AssetInspectionVehicle{})
	subQuery.Joins("LEFT JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(subQuery, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsFromDigiSpect {
		subQuery.Where("ams_asset_inspection_vehicle.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	}

	subQuery.Select(
		"ams_asset_inspection_vehicle.custom_reference_number",
		"COUNT(ams_asset_inspection_vehicle.id) AS inspection_count",
	)

	subQuery.Group("ams_asset_inspection_vehicle.custom_reference_number")

	// Now create the main query to categorize by frequency
	mainQuery := dB.GetTx().Table("(?) AS vehicle_counts", subQuery)

	mainQuery.Select(
		`CASE 
			WHEN inspection_count = 1 THEN '1 Inspection'
			WHEN inspection_count = 2 THEN '2 Inspections'
			WHEN inspection_count = 3 THEN '3 Inspections'
			ELSE '>3 Inspections'
		END AS name`,
		`CASE 
			WHEN inspection_count = 1 THEN '1_INSPECTION'
			WHEN inspection_count = 2 THEN '2_INSPECTION'
			WHEN inspection_count = 3 THEN '3_INSPECTION'
			ELSE 'MT_3_INSPECTION'
		END AS code`,
		"COUNT(*) AS y",
	)

	mainQuery.Group("name, code")
	mainQuery.Order("code ASC")

	var charts []commonmodel.Chart
	err := mainQuery.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetInspectionRepository) ChartInspectionLocations(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]models.InspectionLocationChart, error) {
	query := dB.GetTx().Model(&models.AssetInspection{})

	query.
		Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_asset_inspection_tyre ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:            req.ClientID,
		StartDate1:          req.StartDatetime,
		EndDate1:            req.EndDatetime,
		DigiSpectOnlySource: req.IsFromDigiSpect,
	})

	query.Select(
		"ams_asset_inspections.location_label AS name",
		"ams_asset_inspections.id AS code",
		"ams_asset_inspections.location_lat AS lat",
		"ams_asset_inspections.location_long AS long",
	)

	query.Group("ams_asset_inspections.location_label, ams_asset_inspections.id, ams_asset_inspections.location_lat, ams_asset_inspections.location_long")

	var locations []models.InspectionLocationChart
	if err := query.Scan(&locations).Error; err != nil {
		return nil, err
	}

	return locations, nil
}

func (ar *AssetInspectionRepository) ChartInspectionsByInspectorPerDate(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspection{})

	query.Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_asset_inspection_tyre ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:            req.ClientID,
		StartDate1:          req.StartDatetime,
		EndDate1:            req.EndDatetime,
		DigiSpectOnlySource: req.IsFromDigiSpect,
	})

	query.Select(
		"DATE(ams_asset_inspections.created_at) AS x",
		"ams_asset_inspections.inspect_by_user_id AS code",
		"COUNT(DISTINCT ams_asset_inspections.id) AS y",
	)

	query.Group("DATE(ams_asset_inspections.created_at), ams_asset_inspections.inspect_by_user_id")
	query.Order("x ASC")

	var results []commonmodel.Chart

	if err := query.Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (ar *AssetInspectionRepository) ChartTotalInspections(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspection{})

	query.Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_asset_inspection_tyre ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:            req.ClientID,
		StartDate1:          req.StartDatetime,
		EndDate1:            req.EndDatetime,
		DigiSpectOnlySource: req.IsFromDigiSpect,
	})

	query.Distinct("ams_asset_inspections.id")

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(total),
			Name: "Total Inspections",
			Code: null.StringFrom("TOTAL_INSPECTIONS"),
		},
	}, nil
}

func (ar *AssetInspectionRepository) ChartTotalLinkedTyresInspections(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspection{})

	query.Joins("JOIN ams_asset_inspection_vehicle ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id")
	query.Where("ams_asset_inspection_vehicle.asset_vehicle_id IS NOT NULL")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(total),
			Name: "Total Linked Tyre Inspections",
			Code: null.StringFrom("TOTAL_LINKED_TYRE_INSPECTIONS"),
		},
	}, nil

}

func (ar *AssetInspectionRepository) ChartTotalCustomers(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsFromDigiSpect {
		query.Where("ams_asset_inspection_vehicle.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	}

	// Only count non-empty partner owner names
	query.Where("ams_asset_inspection_vehicle.partner_owner_name IS NOT NULL AND ams_asset_inspection_vehicle.partner_owner_name != ''")

	// Count distinct partner owner names
	var count int64
	err := query.Distinct("ams_asset_inspection_vehicle.partner_owner_name").Count(&count).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(count),
			Name: "Total Unique Customers",
			Code: null.StringFrom("TOTAL_CUSTOMERS"),
		},
	}, nil
}

func (ar *AssetInspectionRepository) ChartTotalVehiclesInspected(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsFromDigiSpect {
		query.Where("ams_asset_inspection_vehicle.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	}

	// Use COALESCE to handle NULL values and prefer custom_reference_number if available
	query.Where("COALESCE(ams_asset_inspection_vehicle.custom_reference_number, ams_asset_inspection_vehicle.custom_serial_number) IS NOT NULL")

	// Count distinct vehicle identifiers
	var count int64
	err := query.Select("COUNT(DISTINCT COALESCE(ams_asset_inspection_vehicle.custom_reference_number, ams_asset_inspection_vehicle.custom_serial_number))").Count(&count).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(count),
			Name: "Total Unique Vehicles Inspected",
			Code: null.StringFrom("TOTAL_VEHICLES_INSPECTED"),
		},
	}, nil
}

func (ar *AssetInspectionRepository) ChartTotalTyresInspected(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionTyre{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsFromDigiSpect {
		query.Where("ams_asset_inspection_tyre.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	}

	// Only count non-empty serial numbers
	query.Where("ams_asset_inspection_tyre.custom_serial_number IS NOT NULL AND ams_asset_inspection_tyre.custom_serial_number != ''")

	// Count distinct tyre serial numbers
	var count int64
	err := query.Distinct("ams_asset_inspection_tyre.custom_serial_number").Count(&count).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(count),
			Name: "Total Unique Tyres Inspected",
			Code: null.StringFrom("TOTAL_TYRES_INSPECTED"),
		},
	}, nil
}

func (ar *AssetInspectionRepository) ChartTotalInspectors(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspection{})

	query.Joins("LEFT JOIN ams_asset_inspection_vehicle ON ams_asset_inspections.id=ams_asset_inspection_vehicle.asset_inspection_id").
		Joins("LEFT JOIN ams_asset_inspection_tyre ON ams_asset_inspections.id=ams_asset_inspection_tyre.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:            req.ClientID,
		StartDate1:          req.StartDatetime,
		EndDate1:            req.EndDatetime,
		DigiSpectOnlySource: req.IsFromDigiSpect,
	})

	// Only count non-empty inspector IDs
	query.Where("ams_asset_inspections.inspect_by_user_id IS NOT NULL AND ams_asset_inspections.inspect_by_user_id != ''")

	// Count distinct inspector IDs
	var count int64
	err := query.Distinct("ams_asset_inspections.inspect_by_user_id").Count(&count).Error
	if err != nil {
		return nil, err
	}

	return []commonmodel.Chart{
		{
			Y:    float64(count),
			Name: "Total Unique Inspectors",
			Code: null.StringFrom("TOTAL_INSPECTORS"),
		},
	}, nil
}

func (ar *AssetInspectionRepository) ChartTop5InspectedCustomers(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(query, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsFromDigiSpect {
		query.Where("ams_asset_inspection_vehicle.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	}

	// Only count non-empty partner owner names
	query.Where("ams_asset_inspection_vehicle.partner_owner_name IS NOT NULL AND ams_asset_inspection_vehicle.partner_owner_name != ''")

	// Group by partner owner name, count inspections, and get top 5
	query.Select(
		"ams_asset_inspection_vehicle.partner_owner_name AS name",
		"COUNT(ams_asset_inspections.id) AS y",
	)
	query.Group("ams_asset_inspection_vehicle.partner_owner_name")
	query.Order("y DESC")
	query.Limit(5)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetInspectionRepository) ChartCustomersByInspectionCount(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error) {
	// First, create a subquery to count inspections per customer
	subQuery := dB.GetTx().Model(&models.AssetInspectionVehicle{}).
		Joins("JOIN ams_asset_inspections ON ams_asset_inspections.id = ams_asset_inspection_vehicle.asset_inspection_id")

	enrichAssetInspectionQueryWithWhere(subQuery, models.AssetInspectionWhere{
		ClientID:   req.ClientID,
		StartDate1: req.StartDatetime,
		EndDate1:   req.EndDatetime,
	})

	if req.IsFromDigiSpect {
		subQuery.Where("ams_asset_inspection_vehicle.source_type_code IN (?)", []string{
			constants.INSPECTION_SOURCE_TRANSLOGIC_APP,
			constants.INSPECTION_SOURCE_ASSETFINDR_APP,
		})
	}

	// Only count non-empty partner owner names
	subQuery.Where("ams_asset_inspection_vehicle.partner_owner_name IS NOT NULL AND ams_asset_inspection_vehicle.partner_owner_name != ''")

	// Group by partner owner name and count inspections
	subQuery.Select(
		"ams_asset_inspection_vehicle.partner_owner_name",
		"COUNT(DISTINCT ams_asset_inspections.id) AS inspection_count",
	)
	subQuery.Group("ams_asset_inspection_vehicle.partner_owner_name")

	// Now create the main query to categorize by inspection count
	mainQuery := dB.GetTx().Table("(?) AS customer_counts", subQuery)

	mainQuery.Select(
		`CASE 
			WHEN inspection_count = 1 THEN '1 Inspection'
			WHEN inspection_count = 2 THEN '2 Inspections'
			WHEN inspection_count = 3 THEN '3 Inspections'
			ELSE '>3 Inspections'
		END AS name`,
		`CASE 
			WHEN inspection_count = 1 THEN '1_INSPECTION'
			WHEN inspection_count = 2 THEN '2_INSPECTION'
			WHEN inspection_count = 3 THEN '3_INSPECTION'
			ELSE 'MT_3_INSPECTION'
		END AS code`,
		"COUNT(*) AS y",
	)

	mainQuery.Group("name, code")
	mainQuery.Order("code ASC")

	var charts []commonmodel.Chart
	err := mainQuery.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	// Ensure all categories are represented
	existingCodes := map[string]bool{}
	for _, chart := range charts {
		existingCodes[chart.Code.String] = true
	}

	requiredCodes := map[string]string{
		"1_INSPECTION":    "1 Inspection",
		"2_INSPECTION":    "2 Inspections",
		"3_INSPECTION":    "3 Inspections",
		"MT_3_INSPECTION": ">3 Inspections",
	}

	for code, name := range requiredCodes {
		if !existingCodes[code] {
			charts = append(charts, commonmodel.Chart{
				Name: name,
				Code: null.StringFrom(code),
				Y:    0,
			})
		}
	}

	return charts, nil
}
