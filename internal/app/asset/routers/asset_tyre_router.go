package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterAssetTyreRoutes(route *gin.Engine, assetTyreHandler *handler.AssetTyreHandler) *gin.Engine {

	assetTyreRoutes := route.Group("/v1/asset-tyres", middleware.TokenValidationMiddleware())
	{
		assetTyreRoutes.GET("", assetTyreHandler.GetAssetTyres)
		assetTyreRoutes.GET("/scrappeds", assetTyreHandler.GetAssetTyreScrappedList)
		assetTyreRoutes.GET("/disposeds", assetTyreHandler.GetAssetTyreDisposedList)
		assetTyreRoutes.POST("", assetTyreHandler.CreateAssetTyre)
		assetTyreRoutes.PUT("/:asset_id", assetTyreHandler.UpdateAssetTyre)
		assetTyreRoutes.GET("/:id", assetTyreHandler.GetAssetTyreByID)
		assetTyreRoutes.POST("/:id/retread", assetTyreHandler.RetreadAssetTyre)
		assetTyreRoutes.PUT("/treads/:id", assetTyreHandler.UpdateRetreadAssetTyre)
		assetTyreRoutes.GET("/byids", assetTyreHandler.GetAssetTyresByIds)
		assetTyreRoutes.GET("/by-linked-parent-asset-id", assetTyreHandler.GetAssetTyresByLinkedParentAssetId)
		assetTyreRoutes.POST("/bulk", assetTyreHandler.BulkUploadAssetTyres)
		assetTyreRoutes.GET("/stats-histories/:asset_id/last-month", assetTyreHandler.GetLastMonthAssetTyreStatsHistory)
		assetTyreRoutes.GET("/:id/export", assetTyreHandler.ExportAssetTyreByID)
	}

	assetTyreTreadConfigRoutes := route.Group("/v1/asset-tyres/treads/configs", middleware.TokenValidationMiddleware())
	{
		assetTyreTreadConfigRoutes.GET("", assetTyreHandler.GetAssetTyresTreadConfigs)
		assetTyreTreadConfigRoutes.GET("/:id", assetTyreHandler.GetAssetTyresTreadConfigByID)
		assetTyreTreadConfigRoutes.POST("", assetTyreHandler.CreateAssetTyresTreadConfig)
		assetTyreTreadConfigRoutes.PUT("/:id", assetTyreHandler.UpdateAssetTyresTreadConfig)
		assetTyreTreadConfigRoutes.DELETE("/:id", assetTyreHandler.DeleteAssetTyresTreadConfig)
		assetTyreTreadConfigRoutes.GET("/brands", assetTyreHandler.GetAssetTyresTreadConfigBrands)
	}

	tyreRoutes := route.Group("/v1/tyre", middleware.TokenValidationMiddleware())
	{
		tyreRoutes.GET("", assetTyreHandler.GetTyreList)
	}

	tyresRoutes := route.Group("/v1/tyres", middleware.TokenValidationMiddleware())
	{
		tyresRoutes.POST("", assetTyreHandler.CreateTyre)
		tyresRoutes.DELETE("/:id", assetTyreHandler.DeleteTyre)
		tyresRoutes.GET("/:id", assetTyreHandler.GetTyre)
		tyresRoutes.GET("/sizes", assetTyreHandler.GetTyreSizeList)
		tyresRoutes.GET("/pattern-types", assetTyreHandler.GetTyrePatternTypes)
		tyresRoutes.PUT("/:id", assetTyreHandler.UpdateTyre)
		tyresRoutes.GET("/export", assetTyreHandler.GetTyreCSV)
		tyresRoutes.GET("/utilization-rate-status", assetTyreHandler.GetUtilizationRatePercentageStatus)

	}

	jobAsetTyreRoutes := route.Group("/v1/jobs/asset-tyres", middleware.APITokenMiddleware)
	{
		jobAsetTyreRoutes.POST("/populate-stats-histories", assetTyreHandler.PopulatePeriodicAssetTyreStatsHistories)
	}

	reportAssetTyreRoutes := route.Group("/v1/reports/asset-tyres", middleware.TokenValidationMiddleware())
	{
		reportAssetTyreRoutes.GET("/stock", assetTyreHandler.GetAssetTyreStockReport)
		reportAssetTyreRoutes.GET("/stock/export", assetTyreHandler.GetAssetTyreStockReportExport)
		reportAssetTyreRoutes.GET("/installed", assetTyreHandler.GetAssetTyreInstalledReport)
		reportAssetTyreRoutes.GET("/installed/export", assetTyreHandler.GetAssetTyreInstalledReportExport)
		reportAssetTyreRoutes.GET("/uninstalled", assetTyreHandler.GetAssetTyreUninstalledReport)
		reportAssetTyreRoutes.GET("/uninstalled/export", assetTyreHandler.GetAssetTyreUninstalledReportExport)
		reportAssetTyreRoutes.GET("/replacement-forecast", assetTyreHandler.GetAssetTyreReplacementForecastReport)
		reportAssetTyreRoutes.GET("/replacement-forecast/export", assetTyreHandler.GetAssetTyreReplacementForecastExport)
		reportAssetTyreRoutes.GET("/usage", assetTyreHandler.GetAssetTyreUsageReport)
		reportAssetTyreRoutes.GET("/usage/export", assetTyreHandler.GetAssetTyreUsageReportExport)
		reportAssetTyreRoutes.GET("/inspection", assetTyreHandler.GetAssetTyreInspectionReport)
		reportAssetTyreRoutes.GET("/inspection/export", assetTyreHandler.GetAssetTyreInspectionReportExport)
		reportAssetTyreRoutes.GET("/rotation", assetTyreHandler.GetAssetTyreRotationReport)
		reportAssetTyreRoutes.GET("/rotation/export", assetTyreHandler.GetAssetTyreRotationReportExport)

	}

	return route
}
