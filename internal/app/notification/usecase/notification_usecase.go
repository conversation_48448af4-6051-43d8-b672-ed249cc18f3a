package usecase

import (
	"assetfindr/internal/app/notification/constants"
	"assetfindr/internal/app/notification/dtos"
	"assetfindr/internal/app/notification/models"
	"assetfindr/internal/app/notification/repository"
	userModel "assetfindr/internal/app/user-identity/models"
	userRepo "assetfindr/internal/app/user-identity/repository"
	internalConstants "assetfindr/internal/constants"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/firebaseApp"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"fmt"
	"os"
	"strings"

	"firebase.google.com/go/v4/messaging"
)

type NotificationUseCase struct {
	DB                     database.DBUsecase
	NotificationRepository repository.NotificationRepository
	UserRepository         userRepo.UserRepository
	EmailRepository        repository.EmailRepository
}

func NewNotificationUsecase(
	DB database.DBUsecase,
	notificationRepo repository.NotificationRepository,
	emailRepo repository.EmailRepository,
	userRepo userRepo.UserRepository,
) NotificationUseCase {
	return NotificationUseCase{
		DB:                     DB,
		NotificationRepository: notificationRepo,
		UserRepository:         userRepo,
		EmailRepository:        emailRepo,
	}
}

func (u *NotificationUseCase) CreateRegisterNotification(ctx context.Context, req dtos.CreateNotificationReq) error {
	if len(req.Items) == 0 {
		return nil
	}

	notifications := make([]models.Notification, 0, len(req.Items))
	userIDs := make([]string, 0, len(notifications))
	for _, req := range req.Items {
		notifications = append(notifications, models.Notification{
			UserID:                 req.UserID,
			SourceCode:             req.SourceCode,
			SourceReferenceID:      req.SourceReferenceID,
			TargetReferenceID:      req.TargetReferenceID,
			TargetURL:              req.TargetURL,
			MessageHeader:          req.MessageHeader,
			MessageBody:            req.MessageBody,
			PushNotifMessageHeader: req.MessageHeader,
			PushNotifMessageBody:   "Wellcome to AssetFindr",
			IsSendToEmail:          req.IsSendToEmail,
			SentToEmailDatetime:    req.SentToEmailDatetime,
			IsSendToApp:            req.IsSendToApp,
			SentToAppDatetime:      req.SentToAppDatetime,
			IsRead:                 req.IsRead,
			ReadDatetime:           req.ReadDatetime,
			ClientID:               req.ClientID,
			TypeCode:               req.TypeCode,
		})

		userIDs = append(userIDs, req.UserID)
	}

	err := u.NotificationRepository.CreateNotifications(ctx, u.DB.DB(), notifications)
	if err != nil {
		commonlogger.Warnf("failed to create notification %v", err)
		return err
	}

	userMap := map[string]userModel.User{}
	users, err := u.UserRepository.GetUsersV2(ctx, u.DB.DB(), userModel.UserCondition{
		Where: userModel.UserWhere{
			IDs:      userIDs,
			ClientID: req.Items[0].ClientID,
		},
		Columns: []string{},
		Preload: userModel.UserPreload{
			ActiveDevices: req.SendToPushNotif,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get user when create notification %v", err)
		return err
	}

	for _, user := range users {
		userMap[user.ID] = user
	}

	for _, notif := range notifications {
		user, ok := userMap[notif.UserID]
		if req.SendToEmail {
			if !ok {
				commonlogger.Warnf("some user isn't found when create notif %v", err)
				continue
			}

			err := u.EmailRepository.SendBasicEmail(ctx, models.BasicEmailParam{
				Destination: models.EmailDestination{
					ToAddresses: []string{user.Email},
				},
				Subject: notif.MessageHeader,
				MainBody: fmt.Sprintf(`
				<br>
				Dear <b>%s</b>,
				<br><br>
				%s
				<br><br>
				Best Regards, <br>
				Sugih Sutjiono <br>
				CEO of AssetFindr <br>
				`, user.GetName(), notif.MessageBody),
			})
			if err != nil {
				commonlogger.Warnf("failed to send email notif %v", err)
			}
		}

		if req.SendToPushNotif {
			if len(user.Devices) == 0 {
				continue
			}

			tokens := make([]string, 0, len(user.Devices))
			for _, device := range user.Devices {
				tokens = append(tokens, device.FirebaseDeviceToken)
			}

			resp, err := firebaseApp.GetMsgClient().SendEachForMulticast(ctx, &messaging.MulticastMessage{
				Tokens: tokens,
				Data: map[string]string{
					"SourceCode":        notif.SourceCode,
					"SourceReferenceID": notif.SourceReferenceID,
					"TargetReferenceID": notif.TargetReferenceID,
					"TargetURL":         notif.TargetURL,
					"MessageHeader":     notif.MessageHeader,
					"MessageBody":       notif.MessageBody,
				},
				Notification: &messaging.Notification{
					Title:    "AssetFindr",
					Body:     notif.MessageHeader,
					ImageURL: "https://assetfindr.com/logo192.png",
				},
			})
			if err != nil {
				commonlogger.Warnf("failed to push notif %v", err)
			}
			logMultiCastResp(resp)

		}

	}

	return nil
}

func (u *NotificationUseCase) CreateUserNotification(ctx context.Context, req dtos.CreateNotificationReq) error {
	notifications := make([]models.Notification, 0, len(req.Items))
	userIDs := make([]string, 0, len(notifications))
	for _, req := range req.Items {
		notifications = append(notifications, models.Notification{
			UserID:                 req.UserID,
			SourceCode:             req.SourceCode,
			SourceReferenceID:      req.SourceReferenceID,
			TargetReferenceID:      req.TargetReferenceID,
			TargetURL:              req.TargetURL,
			MessageHeader:          req.MessageHeader,
			MessageBody:            req.MessageBody,
			PushNotifMessageHeader: req.MessageHeader,
			PushNotifMessageBody:   "Wellcome to AssetFindr",
			IsSendToEmail:          req.IsSendToEmail,
			SentToEmailDatetime:    req.SentToEmailDatetime,
			IsSendToApp:            req.IsSendToApp,
			SentToAppDatetime:      req.SentToAppDatetime,
			IsRead:                 req.IsRead,
			ReadDatetime:           req.ReadDatetime,
			ClientID:               req.ClientID,
		})

		userIDs = append(userIDs, req.UserID)
	}

	err := u.NotificationRepository.CreateNotifications(ctx, u.DB.DB(), notifications)
	if err != nil {
		commonlogger.Warnf("failed to create notification %v", err)
		return err
	}

	userMap := map[string]userModel.User{}
	users, err := u.UserRepository.GetUsersV2(ctx, u.DB.DB(), userModel.UserCondition{
		Where: userModel.UserWhere{
			IDs: userIDs,
		},
		Columns: []string{},
		Preload: userModel.UserPreload{
			ActiveDevices: req.SendToPushNotif,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get user when create notification %v", err)
		return err
	}

	for _, user := range users {
		userMap[user.ID] = user
	}

	for _, notif := range notifications {
		user, ok := userMap[notif.UserID]
		if req.SendToEmail {
			if !ok {
				commonlogger.Warnf("some user isn't found when create notif %v", err)
				continue
			}

			err := u.EmailRepository.SendBasicEmail(ctx, models.BasicEmailParam{
				Destination: models.EmailDestination{
					ToAddresses: []string{user.Email},
				},
				Subject: notif.MessageHeader,
				MainBody: fmt.Sprintf(`
				<br>
				Dear <b>%s</b>,
				<br><br>
				%s
				<br><br>
				Best Regards,<br>
				Assetfindr Team
				<br>
				`, user.GetName(), notif.MessageBody),
			})
			if err != nil {
				commonlogger.Warnf("failed to send email notif %v", err)
			}
		}

		if req.SendToPushNotif {
			if len(user.Devices) == 0 {
				continue
			}

			tokens := make([]string, 0, len(user.Devices))
			for _, device := range user.Devices {
				tokens = append(tokens, device.FirebaseDeviceToken)
			}

			resp, err := firebaseApp.GetMsgClient().SendEachForMulticast(ctx, &messaging.MulticastMessage{
				Tokens: tokens,
				Data: map[string]string{
					"SourceCode":        notif.SourceCode,
					"SourceReferenceID": notif.SourceReferenceID,
					"TargetReferenceID": notif.TargetReferenceID,
					"TargetURL":         notif.TargetURL,
					"MessageHeader":     notif.MessageHeader,
					"MessageBody":       notif.MessageBody,
				},
				Notification: &messaging.Notification{
					Title:    "AssetFindr",
					Body:     notif.MessageHeader,
					ImageURL: "https://assetfindr.com/logo192.png",
				},
			})
			if err != nil {
				commonlogger.Warnf("failed to push notif %v", err)
			}
			logMultiCastResp(resp)
		}

	}

	return nil
}

func (u *NotificationUseCase) CreateNotification(ctx context.Context, req dtos.CreateNotificationReq) error {
	if len(req.Items) == 0 {
		return nil
	}

	notifications := make([]models.Notification, 0, len(req.Items))
	userIDs := make([]string, 0, len(notifications))
	var titleFirebase, bodyFirebase string
	var titleEmail, bodyEmail string
	for _, req := range req.Items {
		item := models.Notification{
			UserID:                 req.UserID,
			SourceCode:             req.SourceCode,
			SourceReferenceID:      req.SourceReferenceID,
			TargetReferenceID:      req.TargetReferenceID,
			TargetURL:              req.TargetURL,
			MessageHeader:          req.MessageHeader,
			MessageBody:            req.MessageBody,
			PushNotifMessageHeader: req.MessageFirebase.Title,
			PushNotifMessageBody:   req.MessageFirebase.Body,
			IsSendToEmail:          req.IsSendToEmail,
			SentToEmailDatetime:    req.SentToEmailDatetime,
			IsSendToApp:            req.IsSendToApp,
			SentToAppDatetime:      req.SentToAppDatetime,
			IsRead:                 req.IsRead,
			ReadDatetime:           req.ReadDatetime,
			ClientID:               req.ClientID,
			TypeCode:               req.TypeCode,
			ContentTypeCode:        req.ContentTypeCode,
			ReferenceCode:          req.ReferenceCode,
			ReferenceValue:         req.ReferenceValue,
		}

		if item.PushNotifMessageHeader == "" {
			item.PushNotifMessageHeader = item.MessageHeader
		}

		notifications = append(notifications, item)

		userIDs = append(userIDs, req.UserID)
		titleEmail = req.MessageHeader
		bodyEmail = req.MessageBody

		titleFirebase = req.MessageFirebase.Title
		bodyFirebase = req.MessageFirebase.Body

		if titleFirebase == "" {
			titleFirebase = req.MessageHeader
		}

		if bodyFirebase == "" {
			bodyFirebase = req.MessageBody
		}

	}

	err := u.NotificationRepository.CreateNotifications(ctx, u.DB.DB(), notifications)
	if err != nil {
		commonlogger.Warnf("CreateNotification.error failed to create notification req: %v, err: %v", notifications, err)
		return err
	}

	userMap := map[string]userModel.User{}
	users, err := u.UserRepository.GetUsersV2(ctx, u.DB.DB(), userModel.UserCondition{
		Where: userModel.UserWhere{
			IDs: userIDs,
		},
		Columns: []string{},
		Preload: userModel.UserPreload{
			ActiveDevices: req.SendToPushNotif,
		},
	})
	if err != nil {
		commonlogger.Warnf("CreateNotification.error failed to get user when create notification req: %v, err: %v", notifications, err)
		return err
	}

	if len(users) == 0 {
		commonlogger.Warnf("CreateNotification.error no user found %v", userIDs)
		return nil
	}

	for _, user := range users {
		userMap[user.ID] = user
	}

	for _, notif := range notifications {
		user, ok := userMap[notif.UserID]
		if req.SendToEmail {
			if !ok {
				commonlogger.Warnf("CreateNotification.error some user isn't found when create notif req: %v, err: %v", notif, err)
				continue
			}

			err := u.EmailRepository.SendBasicEmail(ctx, models.BasicEmailParam{
				Destination: models.EmailDestination{
					ToAddresses: []string{user.Email},
				},
				Subject: titleEmail,
				MainBody: fmt.Sprintf(`
				<br>
				Dear <b>%s</b>,
				<br><br>
				%s
				<br><br>
				Thanks,<br>
				Assetfindr Team
				<br>
				`, user.GetName(), bodyEmail),
			})
			if err != nil {
				commonlogger.Warnf("CreateNotification.error failed to send email notif req: %v, err: %v", notif, err)
			}
		}

		if req.SendToPushNotif {
			if len(user.Devices) == 0 {
				continue
			}

			tokens := make([]string, 0, len(user.Devices))
			for _, device := range user.Devices {
				tokens = append(tokens, device.FirebaseDeviceToken)
			}

			resp, err := firebaseApp.GetMsgClient().SendEachForMulticast(ctx, &messaging.MulticastMessage{
				Tokens: tokens,
				Data: map[string]string{
					"SourceCode":        notif.SourceCode,
					"SourceReferenceID": notif.SourceReferenceID,
					"TargetReferenceID": notif.TargetReferenceID,
					"TargetURL":         notif.TargetURL,
					"MessageHeader":     titleFirebase,
					"MessageBody":       bodyFirebase,
				},
				Notification: &messaging.Notification{
					Title:    "AssetFindr",
					Body:     titleFirebase,
					ImageURL: "https://assetfindr.com/logo192.png",
				},
			})
			if err != nil {
				commonlogger.Warnf("CreateNotification.error failed to push notif req: %v, err: %v", notif, err)
			}

			logMultiCastResp(resp)
		}

	}

	return nil
}

func (u *NotificationUseCase) IsHasUnreadNotifications(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	isHasNew, err := u.NotificationRepository.IsHasUnreadNotifications(ctx, u.DB.DB(), claim.UserID)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: claim.UserID,
		Data:        isHasNew,
	}, nil
}

func (u *NotificationUseCase) GetNotificationList(ctx context.Context, req dtos.GetNotificationListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	total, notifs, err := u.NotificationRepository.GetNotificationList(ctx, u.DB.DB(), models.GetNotificationListParam{
		ListRequest: req.ListRequest,
		Cond: models.NotificationCondition{
			Where: models.NotificationWhere{
				UserID:   claim.UserID,
				ClientID: claim.GetLoggedInClientID(),
				TypeCode: req.TypeCode,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: total,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         notifs,
	}, nil
}

func (u *NotificationUseCase) SetNotificationsAsRead(ctx context.Context, id string) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	return u.NotificationRepository.SetNotificationsAsRead(ctx, u.DB.DB(), claim.UserID, id)
}

func (u *NotificationUseCase) MarkAllNotificationsAsRead(ctx context.Context) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	return u.NotificationRepository.MarkAllNotificationsAsRead(ctx, u.DB.DB(), claim.UserID)
}

func (u *NotificationUseCase) GenerateTargetURL(ctx context.Context, clientAlias, destinationCode, refID string) string {
	apiURL := internalConstants.STAGING_URL
	if appEnv := os.Getenv(internalConstants.ENV_APP_ENV); appEnv == "production" {
		apiURL = clientAlias + internalConstants.BASE_URL
	} else if appEnv == "sandbox" {
		apiURL = internalConstants.SANDBOX_URL
	}

	return fmt.Sprintf("http://%s/#/redirect?destination_code=%s&reference_id=%s", apiURL, destinationCode, refID)
}

func (u *NotificationUseCase) CountUnreadNotifications(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	countUnreadNotifications, err := u.NotificationRepository.CountUnreadNotifications(ctx, u.DB.DB(), claim.UserID)
	if err != nil {
		return nil, err
	}

	resp := dtos.CountUnReadNotifications{}
	for i := range countUnreadNotifications {
		resp.All += countUnreadNotifications[i].Number

		if countUnreadNotifications[i].TypeCode == constants.NOTIFICATION_TYPE_USER_ACTIVITY_CODE {
			resp.UserActivity += countUnreadNotifications[i].Number
		}

		if countUnreadNotifications[i].TypeCode == constants.NOTIFICATION_TYPE_ALERT_CODE {
			resp.Alert += countUnreadNotifications[i].Number
		}

		if countUnreadNotifications[i].TypeCode == constants.NOTIFICATION_TYPE_REMINDER_CODE {
			resp.Reminder += countUnreadNotifications[i].Number
		}

	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: claim.UserID,
		Data:        resp,
	}, nil
}

func (u *NotificationUseCase) GetTicketCount(ctx context.Context, parameters []string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	resp := dtos.NotifCountResp{
		ReminderOverdue: 0,
	}

	isRead := false

	includeReminderOverdue := false
	for _, parameter := range parameters {
		switch strings.ToUpper(parameter) {
		case constants.NOTIF_COUNT_PARAMETER_REMINDER_OVERDUE:
			includeReminderOverdue = true
		}
	}

	if includeReminderOverdue {
		countReminderOverdue, err := u.NotificationRepository.CountNotification(ctx, u.DB.DB(), models.NotificationCondition{
			Where: models.NotificationWhere{
				ClientID: claim.GetLoggedInClientID(),
				UserID:   claim.UserID,
				TypeCode: constants.NOTIFICATION_TYPE_REMINDER_CODE,
				IsRead:   &isRead,
			},
		})
		if err != nil {
			return nil, err
		}

		resp.ReminderOverdue = countReminderOverdue
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: claim.GetLoggedInClientID(),
		Data:        resp,
	}, nil
}

func (u *NotificationUseCase) TestEmail(ctx context.Context, to, hmtlContent string) error {
	err := u.EmailRepository.SendBasicEmail(ctx, models.BasicEmailParam{
		Destination: models.EmailDestination{
			ToAddresses: []string{to},
		},
		Subject:  "Test Email",
		MainBody: hmtlContent,
	})
	return err
}

func logMultiCastResp(resp *messaging.BatchResponse) {
	if resp == nil || resp.FailureCount == 0 {
		return
	}

	for _, item := range resp.Responses {
		if item == nil || item.Success {
			continue
		}

		commonlogger.Warnf("error multicast firebase, err: %s", item.Error)
	}
}

func (u *NotificationUseCase) TestFireBase(ctx context.Context, firebaseToken string) error {
	resp, err := firebaseApp.GetMsgClient().SendEachForMulticast(ctx, &messaging.MulticastMessage{
		Tokens: []string{firebaseToken},
		// resp, err := firebaseApp.GetMsgClient().Send(ctx, &messaging.Message{
		// 	Token: firebaseToken,
		Data: map[string]string{
			"SourceCode":        "",
			"SourceReferenceID": "",
			"TargetReferenceID": "",
			"TargetURL":         "https://app.clickup.com/t/86cx43wcq",
			"MessageHeader":     "Test",
			"MessageBody":       "test",
		},
		Notification: &messaging.Notification{
			Title:    "Test Title",
			Body:     "Test Body",
			ImageURL: "https://assetfindr.com/logo192.png",
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to push notif %v", err)
		return err
	}

	logMultiCastResp(resp)
	return nil
}
