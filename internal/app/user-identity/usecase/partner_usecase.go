package usecase

import (
	assetModel "assetfindr/internal/app/asset/models"
	assetRepo "assetfindr/internal/app/asset/repository"
	integrationConstants "assetfindr/internal/app/integration/constants"
	integrationModels "assetfindr/internal/app/integration/models"
	integrationRepo "assetfindr/internal/app/integration/repository"
	inventoryRepo "assetfindr/internal/app/inventory/repository"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUseCase "assetfindr/internal/app/storage/usecase"
	taskRepo "assetfindr/internal/app/task/repository"
	"assetfindr/internal/app/user-identity/constants"
	"assetfindr/internal/app/user-identity/dtos"
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
)

type PartnerUseCase struct {
	DB                   database.DBUsecase
	partnerRepo          repository.PartnerRepository
	contactRepo          repository.ContactRepository
	partnerContactRepo   repository.PartnerContactRepository
	accurateRepo         integrationRepo.AccurateRepository
	integrationRepo      integrationRepo.IntegrationRepository
	assetRepo            assetRepo.AssetRepository
	assetTransactionRepo assetRepo.AssetTransactionRepository
	assetTyreTreadRepo   assetRepo.AssetTyreRepository
	ticketRepo           taskRepo.TicketRepository
	packageRepo          inventoryRepo.PackageRepository
	attachmentUseCase    *storageUseCase.AttachmentUseCase
}

func NewPartnerUseCase(
	DB database.DBUsecase,
	partnerRepo repository.PartnerRepository,
	contactRepo repository.ContactRepository,
	partnerContactRepo repository.PartnerContactRepository,
	accurateRepo integrationRepo.AccurateRepository,
	integrationRepo integrationRepo.IntegrationRepository,
	assetRepo assetRepo.AssetRepository,
	assetTransactionRepo assetRepo.AssetTransactionRepository,
	assetTyreTreadRepo assetRepo.AssetTyreRepository,
	ticketRepo taskRepo.TicketRepository,
	packageRepo inventoryRepo.PackageRepository,
	attachmentUseCase *storageUseCase.AttachmentUseCase,
) *PartnerUseCase {
	return &PartnerUseCase{
		DB:                   DB,
		partnerRepo:          partnerRepo,
		contactRepo:          contactRepo,
		partnerContactRepo:   partnerContactRepo,
		accurateRepo:         accurateRepo,
		integrationRepo:      integrationRepo,
		assetRepo:            assetRepo,
		assetTransactionRepo: assetTransactionRepo,
		assetTyreTreadRepo:   assetTyreTreadRepo,
		ticketRepo:           ticketRepo,
		packageRepo:          packageRepo,
		attachmentUseCase:    attachmentUseCase,
	}
}

func (uc *PartnerUseCase) constructContact(req dtos.CreatePartner) ([]models.Contact, error) {
	contacts := make([]models.Contact, 0)
	for _, val := range req.ContactList {
		contact := models.Contact{}
		contact.Name = val.Name
		contact.PhoneNumber = val.PhoneNumber
		contact.Email = val.Email
		contact.TaxIdentity = val.TaxIdentity
		contact.IdentificationNumber = val.IdentificationNumber
		contact.Role = val.Role
		contacts = append(contacts, contact)
	}
	return contacts, nil
}

func (uc *PartnerUseCase) constructPartner(req dtos.CreatePartner) (*models.Partner, error) {
	partner := models.Partner{}
	partner.Name = req.Name
	partner.ServiceProvided = req.ServiceProvided
	partner.StatusCode = req.StatusCode
	partner.Address = req.Address
	partner.Floor = sqlhelpers.NullString(req.Floor)
	partner.Unit = sqlhelpers.NullString(req.Unit)
	partner.MapLat = req.MapLat
	partner.MapLong = req.MapLong
	partner.PhoneNumber1 = req.PhoneNumber1
	partner.PhoneNumber2 = sqlhelpers.NullString(req.PhoneNumber2)
	partner.Email = req.Email
	partner.Notes = req.Notes
	if req.PartnerTypeCode == "" {
		partner.PartnerTypeCode = constants.PARTNER_TYPE_CODE_VENDOR
	} else {
		partner.PartnerTypeCode = req.PartnerTypeCode
	}
	partner.PICName = req.PICName
	partner.AddressMapLink = req.AddressMapLink
	partner.TaxIdentity = req.TaxIdentity
	return &partner, nil
}

func (uc *PartnerUseCase) constructPartnerContact(partnerID string, contact []models.Contact) ([]models.PartnerContact, error) {
	partnercontacts := make([]models.PartnerContact, 0)
	for _, val := range contact {
		partnercontact := models.PartnerContact{}
		partnercontact.PartnerID = partnerID
		partnercontact.ContactID = val.ID
		partnercontacts = append(partnercontacts, partnercontact)
	}
	return partnercontacts, nil
}

func (uc *PartnerUseCase) CreatePartnerCustomer(ctx context.Context, req dtos.CreateUpdatePartnerCustomer) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tX.Rollback()
	contacts := make([]models.Contact, 0, len(req.ContactList))
	for i := range req.ContactList {
		contacts = append(contacts, models.Contact{
			Name:        req.ContactList[i].Name,
			PhoneNumber: req.ContactList[i].PhoneNumber,
			Email:       req.ContactList[i].Email,
			Role:        req.ContactList[i].Role,
		})
	}
	partner := &models.Partner{
		Name:            req.Name,
		StatusCode:      constants.PARTNER_STATUS_CODE_ACTIVE,
		PartnerTypeCode: constants.PARTNER_TYPE_CODE_CUSTOMER,
		Address:         req.Address,
		PhoneNumber1:    req.PhoneNumber1,
		Email:           req.Email,
		TaxIdentity:     req.TaxIdentity,
		PICName:         req.PICName,
		AddressMapLink:  req.AddressMapLink,
	}
	err = uc.partnerRepo.CreatePartner(ctx, tX.DB(), partner)
	if err != nil {
		return nil, err
	}

	err = uc.contactRepo.CreateContact(ctx, tX.DB(), contacts)
	if err != nil {
		return nil, err
	}
	partnerContact, err := uc.constructPartnerContact(partner.ID, contacts)
	if err != nil {
		return nil, err
	}
	err = uc.partnerContactRepo.CreatePartnerContact(ctx, tX.DB(), partnerContact)
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUseCase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_PARTNER_CUSTOMER,
		SourceReferenceID: partner.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: partner.ID,
		Data:        nil,
	}, nil
}

func (uc *PartnerUseCase) UpdatePartnerCustomer(ctx context.Context, id string, req dtos.CreateUpdatePartnerCustomer) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tX.Rollback()

	_, err = uc.partnerRepo.GetPartner(ctx, tX.DB(), models.PartnerCondition{
		Where: models.PartnerWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	newContacts := make([]models.Contact, 0, len(req.ContactList))
	deleteContactIDs := []string{}
	for i := range req.ContactList {
		if req.ContactList[i].IsDeleted {
			deleteContactIDs = append(deleteContactIDs, req.ContactList[i].ID)
			continue
		}

		if req.ContactList[i].ID != "" {
			err := uc.contactRepo.UpdateContact(ctx, tX.DB(),
				req.ContactList[i].ID, &models.Contact{
					Name:        req.ContactList[i].Name,
					PhoneNumber: req.ContactList[i].PhoneNumber,
					Email:       req.ContactList[i].Email,
					Role:        req.ContactList[i].Role,
				})
			if err != nil {
				return nil, err
			}
			continue
		}

		newContacts = append(newContacts, models.Contact{
			Name:        req.ContactList[i].Name,
			PhoneNumber: req.ContactList[i].PhoneNumber,
			Email:       req.ContactList[i].Email,
			Role:        req.ContactList[i].Role,
		})
	}

	if len(deleteContactIDs) > 0 {
		err := uc.contactRepo.DeleteContactV2(ctx, tX.DB(), deleteContactIDs)
		if err != nil {
			return nil, err
		}

		err = uc.partnerContactRepo.DeletePartnerContactByContactIDs(ctx, tX.DB(), deleteContactIDs)
		if err != nil {
			return nil, err
		}
	}

	err = uc.partnerRepo.UpdatePartner(ctx, tX.DB(), id, &models.Partner{
		Name:            req.Name,
		StatusCode:      constants.PARTNER_STATUS_CODE_ACTIVE,
		PartnerTypeCode: constants.PARTNER_TYPE_CODE_CUSTOMER,
		Address:         req.Address,
		PhoneNumber1:    req.PhoneNumber1,
		Email:           req.Email,
		TaxIdentity:     req.TaxIdentity,
		PICName:         req.PICName,
		AddressMapLink:  req.AddressMapLink,
	})
	if err != nil {
		return nil, err
	}

	err = uc.contactRepo.CreateContact(ctx, tX.DB(), newContacts)
	if err != nil {
		return nil, err
	}
	newPartnerContact, err := uc.constructPartnerContact(id, newContacts)
	if err != nil {
		return nil, err
	}
	err = uc.partnerContactRepo.CreatePartnerContact(ctx, tX.DB(), newPartnerContact)
	if err != nil {
		return nil, err
	}

	_, err = uc.attachmentUseCase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_PARTNER_CUSTOMER,
		SourceReferenceID: id,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})
	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *PartnerUseCase) UpdatePartnerCustomerToAccurate(ctx context.Context, id string, req dtos.CreateUpdatePartnerCustomer) (*commonmodel.CreateResponse, error) {
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}

	accurateHeaders, err := uc.GetAccurateHeaders(ctx)
	if err != nil {
		return nil, err
	}

	accurateCustomerContacts := make([]integrationModels.AccurateCustomerContact, 0, len(req.ContactList))
	for i := range req.ContactList {
		idInt := 0
		if req.ContactList[i].ID != "" {
			idInt, err = strconv.Atoi(req.ContactList[i].ID)
			if err != nil {
				return nil, err
			}
		}

		accurateCustomerContact := integrationModels.AccurateCustomerContact{
			Id:          idInt,
			Name:        req.ContactList[i].Name,
			Position:    req.ContactList[i].Role,
			Email:       req.ContactList[i].Email,
			HomePhone:   "",
			MobilePhone: req.ContactList[i].PhoneNumber,
			WorkPhone:   "",
		}
		if req.ContactList[i].IsDeleted {
			accurateCustomerContact.SetToBeDelete()
		}

		accurateCustomerContacts = append(accurateCustomerContacts, accurateCustomerContact)
	}

	accurateCustomer := integrationModels.AccurateCustomer{
		ID:             idInt,
		Name:           req.Name,
		Email:          req.Email,
		MobilePhone:    "",
		WorkPhone:      req.PhoneNumber1,
		NpwpNo:         req.TaxIdentity,
		BillStreet:     req.Address,
		BillCity:       req.AddressCity,
		BillProvince:   req.AddressProvince,
		BillCountry:    req.AddressCountry,
		BillZipCode:    req.AddressZipCode,
		ShipSameAsBill: false,
		TaxSameAsBill:  false,
		TermName:       req.TermName,
		DetailContact:  accurateCustomerContacts,
	}
	resp, err := uc.accurateRepo.SaveCustomer(ctx, accurateCustomer, *accurateHeaders)
	if err != nil {
		return nil, err
	}

	if req.Name != "" {
		err := uc.UpdateCustomerNameRelatedData(ctx, id, req.Name)
		if err != nil {
			return nil, err
		}
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: strconv.Itoa(resp.ID),
		Data:        nil,
	}, nil
}

func (uc *PartnerUseCase) UpdateCustomerNameRelatedData(ctx context.Context, id string, name string) error {
	err := uc.ticketRepo.UpdateTicketPartnerOwnerName(ctx, uc.DB.DB(), id, name)
	if err != nil {
		return err
	}

	err = uc.assetRepo.UpdateAssetPartnerOwnerName(ctx, uc.DB.DB(), id, name)
	if err != nil {
		return err
	}

	err = uc.packageRepo.UpdateMembershipPartnerOwnerName(ctx, uc.DB.DB(), id, name)
	if err != nil {
		return err
	}

	return nil
}

func (uc *PartnerUseCase) CreatePartnerCustomerToAccurate(ctx context.Context, req dtos.CreateUpdatePartnerCustomer) (*commonmodel.CreateResponse, error) {
	accurateHeaders, err := uc.GetAccurateHeaders(ctx)
	if err != nil {
		return nil, err
	}

	accurateCustomerContacts := make([]integrationModels.AccurateCustomerContact, 0, len(req.ContactList))
	for i := range req.ContactList {
		accurateCustomerContacts = append(accurateCustomerContacts, integrationModels.AccurateCustomerContact{
			Name:        req.ContactList[i].Name,
			Position:    req.ContactList[i].Role,
			Email:       req.ContactList[i].Email,
			HomePhone:   "",
			MobilePhone: req.ContactList[i].PhoneNumber,
			WorkPhone:   "",
		})
	}

	accurateCustomer := integrationModels.AccurateCustomer{
		Name:           req.Name,
		Email:          req.Email,
		MobilePhone:    "",
		WorkPhone:      req.PhoneNumber1,
		NpwpNo:         req.TaxIdentity,
		BillStreet:     req.Address,
		BillCity:       req.AddressCity,
		BillProvince:   req.AddressProvince,
		BillCountry:    req.AddressCountry,
		BillZipCode:    req.AddressZipCode,
		TermName:       req.TermName,
		ShipSameAsBill: false,
		TaxSameAsBill:  false,
		DetailContact:  accurateCustomerContacts,
	}
	resp, err := uc.accurateRepo.SaveCustomer(ctx, accurateCustomer, *accurateHeaders)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: strconv.Itoa(resp.ID),
		Data:        nil,
	}, nil
}

func (uc *PartnerUseCase) CreatePartner(ctx context.Context, req dtos.CreatePartner) (*commonmodel.CreateResponse, error) {
	partner, err := uc.constructPartner(req)
	if err != nil {
		return nil, err
	}
	contact, err := uc.constructContact(req)
	if err != nil {
		return nil, err
	}
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tX.Rollback()
	err = uc.partnerRepo.CreatePartner(ctx, tX.DB(), partner)
	if err != nil {
		return nil, err
	}
	err = uc.contactRepo.CreateContact(ctx, tX.DB(), contact)
	if err != nil {
		return nil, err
	}
	partnerContact, err := uc.constructPartnerContact(partner.ID, contact)
	if err != nil {
		return nil, err
	}
	err = uc.partnerContactRepo.CreatePartnerContact(ctx, tX.DB(), partnerContact)
	if err != nil {
		return nil, err
	}
	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: partner.ID,
		Data:        nil,
	}, nil
}

func (uc *PartnerUseCase) DeletetePartner(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	partner, err := uc.partnerRepo.GetPartner(ctx, uc.DB.DB(), models.PartnerCondition{
		Where: models.PartnerWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	// VALIDATE IF PARTNER IS IN USE
	totalTrx, _, err := uc.assetTransactionRepo.GetAssetTransactionList(ctx, uc.DB.DB(), assetModel.GetAssetTransactionListParam{
		ListRequest: commonmodel.ListRequest{
			PageNo:   1,
			PageSize: 100,
		},
		Cond: assetModel.AssetTransactionCondition{
			Where: assetModel.AssetTransactionWhere{
				ClientID:  claim.GetLoggedInClientID(),
				PartnerID: partner.ID,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	if totalTrx > 0 {
		err = errorhandler.ErrBadRequest("PARTNER_IS_IN_USE")
		return nil, err
	}
	totalAssetTyreTreads, _, err := uc.assetTyreTreadRepo.GetAssetTyreTreadList(ctx, uc.DB.DB(), assetModel.GetAssetTyreTreadListParam{
		ListRequest: commonmodel.ListRequest{
			PageNo:   1,
			PageSize: 100,
		},
		Cond: assetModel.AssetTyreTreadCondition{
			Where: assetModel.AssetTyreTreadWhere{
				ClientID:  claim.GetLoggedInClientID(),
				PartnerID: partner.ID,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	if totalAssetTyreTreads > 0 {
		err = errorhandler.ErrBadRequest("PARTNER_IS_IN_USE")
		return nil, err
	}

	if partner.StatusCode == constants.PARTNER_STATUS_CODE_DELETED {
		return &commonmodel.DeleteResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: id,
		}, nil
	}

	updatePartner := models.Partner{
		StatusCode: constants.PARTNER_STATUS_CODE_DELETED,
	}
	err = uc.partnerRepo.UpdatePartner(ctx, uc.DB.DB(), id, &updatePartner)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *PartnerUseCase) GetPartner(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	partner, err := uc.partnerRepo.GetPartner(ctx, uc.DB.DB(), models.PartnerCondition{
		Where: models.PartnerWhere{
			ID:          id,
			ClientID:    claim.GetLoggedInClientID(),
			ShowDeleted: true,
		},
	})
	if err != nil {
		return nil, err
	}

	dto := dtos.GetPartner{}
	dto.Set(*partner)
	respData := dto

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil

}

func (uc *PartnerUseCase) GetPartners(ctx context.Context, req dtos.PartnerListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if req.PartnerTypeCode == "" {
		req.PartnerTypeCode = constants.PARTNER_TYPE_CODE_VENDOR
	}
	count, partners, err := uc.partnerRepo.GetPartnerList(ctx, uc.DB.DB(), models.GetPartnerListParam{
		ListRequest: req.ListRequest,
		Cond: models.PartnerCondition{
			Where: models.PartnerWhere{
				ClientID:        claim.GetLoggedInClientID(),
				ShowDeleted:     req.ShowDeleted,
				StatusCode:      req.StatusCode,
				PartnerTypeCode: req.PartnerTypeCode,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	response := []dtos.GetPartner{}
	for _, val := range partners {
		dto := dtos.GetPartner{}
		dto.Set(val)
		response = append(response, dto)
	}
	respData := response

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *PartnerUseCase) GetPartnerCustomer(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	partner, err := uc.partnerRepo.GetPartner(ctx, uc.DB.DB(), models.PartnerCondition{
		Where: models.PartnerWhere{
			ID:              id,
			ClientID:        claim.GetLoggedInClientID(),
			PartnerTypeCode: constants.PARTNER_TYPE_CODE_CUSTOMER,
			ShowDeleted:     true,
		},
	})
	if err != nil {
		return nil, err
	}

	contants := []dtos.GetCustomerContact{}
	for i := range partner.Contacts {
		contants = append(contants, dtos.GetCustomerContact{
			ID:          partner.Contacts[i].ID,
			Name:        partner.Contacts[i].Name,
			PhoneNumber: partner.Contacts[i].PhoneNumber,
			Email:       partner.Contacts[i].Email,
			Role:        partner.Contacts[i].Role,
		})
	}

	respData := dtos.GetPartnerCustomer{
		Name:           partner.Name,
		Address:        partner.Address,
		PhoneNumber1:   partner.PhoneNumber1,
		Email:          partner.Email,
		TaxIdentity:    partner.TaxIdentity,
		ContactList:    contants,
		PICName:        partner.PICName,
		AddressMapLink: partner.AddressMapLink,
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil
}

func (uc *PartnerUseCase) GetPartnerCustomerFromAccurate(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	accurateHeaders, err := uc.GetAccurateHeaders(ctx)
	if err != nil {
		return nil, err
	}

	customer, err := uc.accurateRepo.GetCustomer(ctx, id, *accurateHeaders)
	if err != nil {
		return nil, err
	}

	contacts := []dtos.GetCustomerContact{}
	for i := range customer.DetailContact {
		contacts = append(contacts, dtos.GetCustomerContact{
			ID:          strconv.Itoa(customer.DetailContact[i].Id),
			Name:        customer.DetailContact[i].Name,
			PhoneNumber: customer.DetailContact[i].MobilePhone,
			Email:       customer.DetailContact[i].Email,
			Role:        customer.DetailContact[i].Position,
		})
	}
	respData := dtos.GetPartnerCustomer{
		Name:            customer.Name,
		Address:         customer.BillStreet,
		AddressCity:     customer.BillCity,
		AddressCountry:  customer.BillCountry,
		AddressProvince: customer.BillProvince,
		AddressZipCode:  customer.BillZipCode,
		PhoneNumber1:    customer.WorkPhone,
		Email:           customer.Email,
		TaxIdentity:     customer.NpwpNo,
		TermName:        customer.Term.Name,
		ContactList:     contacts,
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil
}

func (uc *PartnerUseCase) GetPartnerCustomerList(ctx context.Context, req dtos.PartnerCustomerListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, partners, err := uc.partnerRepo.GetPartnerList(ctx, uc.DB.DB(), models.GetPartnerListParam{
		ListRequest: req.ListRequest,
		Cond: models.PartnerCondition{
			Where: models.PartnerWhere{
				ClientID:        claim.GetLoggedInClientID(),
				PartnerTypeCode: constants.PARTNER_TYPE_CODE_CUSTOMER,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	response := []dtos.GetPartnerCustomerList{}
	for _, partner := range partners {
		response = append(response, dtos.GetPartnerCustomerList{
			ID:             partner.ID,
			PartnerNo:      "",
			Name:           partner.Name,
			IsActive:       partner.StatusCode == constants.STATUS_ACTIVE,
			PhoneNumber1:   partner.PhoneNumber1,
			PhoneNumber2:   partner.PhoneNumber2.String,
			Email:          partner.Email,
			TaxIdentity:    partner.TaxIdentity,
			Address:        partner.Address,
			PICName:        partner.PICName,
			AddressMapLink: partner.AddressMapLink,
		})
	}
	respData := response

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *PartnerUseCase) GetAccurateHeaders(ctx context.Context) (*commonmodel.AccurateHeaders, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	integrationSession, err := uc.integrationRepo.GetIntegrationSession(ctx, uc.DB.DB(), integrationModels.IntegrationSessionCondition{
		Where: integrationModels.IntegrationSessionWhere{
			ClientID:        claim.GetLoggedInClientID(),
			SessionTypeCode: integrationConstants.INTEGRATION_SESSION_TYPE_ACCURATE,
		},
	})
	if err != nil {
		return nil, err
	}

	accurateOpenDBResp := integrationModels.AccurateOpenDBResp{}
	err = json.Unmarshal(integrationSession.Data.Bytes, &accurateOpenDBResp)
	if err != nil {
		return nil, err
	}

	return &commonmodel.AccurateHeaders{
		Session: accurateOpenDBResp.Session,
		Token:   accurateOpenDBResp.Token,
		Host:    accurateOpenDBResp.Host,
	}, nil
}

func (uc *PartnerUseCase) GetCustomerListAccurate(ctx context.Context, req dtos.PartnerCustomerListReq) (*commonmodel.ListResponse, error) {
	accurateHeaders, err := uc.GetAccurateHeaders(ctx)
	if err != nil {
		return nil, err
	}

	count, customers, err := uc.accurateRepo.GetCustomerList(ctx, integrationModels.GetAccurateCustomerListParam{
		ListRequest:     req.ListRequest,
		AccurateHeaders: *accurateHeaders,
	})
	if err != nil {
		return nil, err
	}
	response := []dtos.GetPartnerCustomerList{}
	for _, customer := range customers {
		response = append(response, dtos.GetPartnerCustomerList{
			ID:           fmt.Sprintf("%d", customer.ID),
			PartnerNo:    customer.CustomerNo,
			Name:         customer.Name,
			IsActive:     !customer.Suspended,
			PhoneNumber1: customer.WorkPhone,
			PhoneNumber2: customer.MobilePhone,
			Email:        customer.Email,
			TaxIdentity:  customer.NpwpNo,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         response,
	}, nil
}

func (uc *PartnerUseCase) GetPartnerCustomerAccuratePaymentTermList(ctx context.Context, req commonmodel.ListRequest) (*commonmodel.ListResponse, error) {

	accurateHeaders, err := uc.GetAccurateHeaders(ctx)
	if err != nil {
		return nil, err
	}

	count, customers, err := uc.accurateRepo.GetCustomerPaymentTermList(ctx, integrationModels.GetAccurateCustomerPaymentTermListParam{
		ListRequest:     req,
		AccurateHeaders: *accurateHeaders,
	})
	if err != nil {
		return nil, err
	}

	response := []dtos.GetPartnerCustomerPaymentTermList{}
	for _, customer := range customers {
		response = append(response, dtos.GetPartnerCustomerPaymentTermList{
			ID:       fmt.Sprintf("%d", customer.ID),
			Name:     customer.Name,
			IsActive: !customer.Suspended,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         response,
	}, nil
}

func (uc *PartnerUseCase) GetPartnerTypes(ctx context.Context) (*commonmodel.ListResponse, error) {
	partnerTypes, err := uc.partnerRepo.GetPartnerTypeList(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(partnerTypes),
		PageSize:     len(partnerTypes),
		PageNo:       1,
		Data:         partnerTypes,
	}, nil

}

func (uc *PartnerUseCase) UpdatePartner(ctx context.Context, id string, req dtos.UpdatePartner) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()
	partner, err := uc.partnerRepo.GetPartner(ctx, tx.DB(), models.PartnerCondition{
		Where: models.PartnerWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	createDTO := req.ToCreate()
	newPartner, err := uc.constructPartner(createDTO)
	if err != nil {
		return nil, err
	}
	err = uc.partnerRepo.UpdatePartner(ctx, tx.DB(), id, newPartner)
	if err != nil {
		return nil, err
	}

	contacts, err := uc.constructContact(createDTO)
	if err != nil {
		return nil, err
	}
	switch {
	case len(contacts) == 0:
		if len(partner.Contacts) > 0 {
			err := uc.contactRepo.DeleteContact(ctx, tx.DB(), partner.Contacts)

			if err != nil {
				return nil, err
			}
			partnerContacts, err := uc.partnerContactRepo.GetPartnerContact(ctx, tx.DB(), models.PartnerContactCondition{
				Where: models.PartnerContactWhere{
					PartnerID: id,
					ClientID:  claim.GetLoggedInClientID(),
				},
			})
			if err != nil {
				return nil, err
			}
			err = uc.partnerContactRepo.DeletePartnerContact(ctx, tx.DB(), partnerContacts)
			if err != nil {
				return nil, err
			}
		}
	case len(contacts) > len(partner.Contacts):
		for pos, val := range partner.Contacts {
			err := uc.contactRepo.UpdateContact(ctx, tx.DB(), val.ID, &contacts[pos])
			if err != nil {
				return nil, err
			}
		}
		err = uc.contactRepo.CreateContact(ctx, tx.DB(), contacts[len(partner.Contacts):])
		if err != nil {
			return nil, err
		}
		partnerContacts, err := uc.constructPartnerContact(id, contacts[len(partner.Contacts):])
		if err != nil {
			return nil, err
		}
		err = uc.partnerContactRepo.CreatePartnerContact(ctx, tx.DB(), partnerContacts)
		if err != nil {
			return nil, err
		}
	case len(contacts) < len(partner.Contacts):
		for pos, val := range contacts {
			err := uc.contactRepo.UpdateContact(ctx, tx.DB(), partner.Contacts[pos].ID, &val)
			if err != nil {
				return nil, err
			}
		}
		err := uc.contactRepo.DeleteContact(ctx, tx.DB(), partner.Contacts[len(contacts):])
		if err != nil {
			return nil, err
		}
		partnerContacts, err := uc.partnerContactRepo.GetPartnerContact(ctx, tx.DB(), models.PartnerContactCondition{
			Where: models.PartnerContactWhere{
				PartnerID: id,
				ClientID:  claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}
		err = uc.partnerContactRepo.DeletePartnerContact(ctx, tx.DB(), partnerContacts[len(contacts):])
		if err != nil {
			return nil, err
		}
	default:
		for pos, val := range partner.Contacts {
			err := uc.contactRepo.UpdateContact(ctx, tx.DB(), val.ID, &contacts[pos])
			if err != nil {
				return nil, err
			}
		}

	}
	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil

}

func (uc *PartnerUseCase) ChartTotalPartnerCustomer(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, _, err := uc.partnerRepo.GetPartnerList(ctx, uc.DB.DB(), models.GetPartnerListParam{
		ListRequest: commonmodel.ListRequest{
			PageSize: 0,
			PageNo:   0,
		},
		Cond: models.PartnerCondition{
			Where: models.PartnerWhere{
				ClientID:        claim.GetLoggedInClientID(),
				PartnerTypeCode: constants.PARTNER_TYPE_CODE_CUSTOMER,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	charts := []commonmodel.Chart{
		{
			Y:    float64(count),
			Name: "Total Registered Customers",
		},
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}
