package routers

import (
	"assetfindr/internal/app/user-identity/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterPartnerRoutes(route *gin.Engine, partnerHandler *handler.PartnerHandler) *gin.Engine {

	partnerRoutes := route.Group("/v1/partners", middleware.TokenValidationMiddleware())
	{
		partnerRoutes.GET("", partnerHandler.GetPartners)
		partnerRoutes.GET("/types", partnerHandler.GetPartnerTypes)
		partnerRoutes.GET("/:id", partnerHandler.GetPartner)
		partnerRoutes.POST("", partnerHandler.CreatePartner)
		partnerRoutes.PUT("/:id", partnerHandler.UpdatePartner)
		partnerRoutes.DELETE("/:id", partnerHandler.DeletePartner)
	}

	partnerCustomerRoutes := route.Group("/v1/partners/customer", middleware.TokenValidationMiddleware())
	{
		partnerCustomerRoutes.GET("", partnerHandler.GetPartnerCustomerList)
		partnerCustomerRoutes.GET("/:id", partnerHandler.GetPartnerCustomer)
		partnerCustomerRoutes.POST("", partnerHandler.CreatePartnerCustomer)
		partnerCustomerRoutes.PUT("/:id", partnerHandler.UpdatePartnerCustomer)

		partnerCustomerRoutes.GET("/accurate-payment-terms", partnerHandler.GetPartnerCustomerAccuratePaymentTermList)
	}

	partnerChartRoute := route.Group("/v1/partners/customer/charts", middleware.TokenValidationMiddleware())
	{
		partnerChartRoute.GET("/total", partnerHandler.ChartTotalPartnerCustomer)
	}

	return route
}
