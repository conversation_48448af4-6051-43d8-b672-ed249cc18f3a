ALTER TABLE ams_asset_inspection_vehicle
ADD COLUMN IF NOT EXISTS partner_owner_id VARCHAR(40);

WITH cte AS (
    SELECT 
       aaiv.id,
       aa.partner_owner_id
    FROM
        ams_asset_inspection_vehicle aaiv
    JOIN ams_assets aa ON
        aa.id = aaiv.asset_vehicle_id
        AND aa.deleted_at IS NULL
        AND aa.partner_owner_id IS NOT NULL
        AND aa.partner_owner_name = aaiv.partner_owner_name
)
UPDATE
    ams_asset_inspection_vehicle
SET
    partner_owner_id = cte.partner_owner_id
FROM
    cte
WHERE
    cte.id = ams_asset_inspection_vehicle.id;


INSERT INTO "uis_ANALYTIC_TYPES"
(code, "label", description)
VALUES
    ('DASHBOARD_TYRE_DEALER_CHART', 'Tyre Inspection Dashboard Chart', '-')
ON CONFLICT (code) DO NOTHING;

INSERT
INTO
    "uis_ANALYTICS"
(code, "label", description, sequence, analytic_type_code)
VALUES
    ('VEHICLE_INSPECTIONS_INSPECTED_CUSTOMER_OVER_TIME', 'Vehicle Inspections & Inspected Customers Over Time', '-', 1, 'DASHBOARD_TYRE_DEALER_CHART')
ON CONFLICT (code) DO NOTHING;

