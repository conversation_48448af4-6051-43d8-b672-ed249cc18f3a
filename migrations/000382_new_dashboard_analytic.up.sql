BEGIN;


INSERT INTO
   "uis_ANALYTIC_TYPES" (code, "label", description)
VALUES
    ('DASHBOARD_TYRE_DEALER_CARD', 'Tyre Inspection Dashboard', '-')
ON CONFLICT (code) DO NOTHING;


INSERT INTO
    "uis_ANALYTICS" (code, "label", description, sequence, analytic_type_code)
VALUES
    ('TOTAL_LINKED_TYRE_INSPECTIONS', 'Total Inspection (This Month)', '-', 1, 'DASHBOARD_TYRE_DEALER_CARD'),
    ('COUNT_OVERDUE_TASKS', 'Overdue Scheduled Tasks', '-', 2, 'DASHBOARD_TYRE_DEALER_CARD'),
    ('COUNT_OPEN_TASKS_TODAY', 'Today’s Scheduled Open Tasks', '-', 3, 'DASHBOARD_TYRE_DEALER_CARD'),
    ('COUNT_OPEN_TASKS', ' Number of Open Tasks', '-', 4, 'DASHBOARD_TYRE_DEALER_CARD'),
    ('TOTAL_REGISTERED_CUSTOMERS', 'Total Registered Customers', '-', 5, 'DASHBOARD_TYRE_DEALER_CARD'),
    ('TOTAL_REGISTERED_VEHICLES', 'Total Registered Vehicles', '-', 6, 'DASHBOARD_TYRE_DEALER_CARD'),
    ('TOTAL_REGISTERED_RUNNING_TYRES_EXC_SPARES', 'Total Registered Running Tyres (Exc. Spares)', '-', 7, 'DASHBOARD_TYRE_DEALER_CARD'),
    ('TOTAL_RUNNING_TYRES_CRITICAL_TREAD_DEPTH_EXC_SPARES', 'Running Tyres with Critical Tread Depth (Exc. Spares)', '-', 8, 'DASHBOARD_TYRE_DEALER_CARD')
ON CONFLICT (code) DO NOTHING;

COMMIT;